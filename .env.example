# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# App Configuration
APP_NAME=City POS
APP_VERSION=1.0.0
APP_ENV=development

# Supabase Configuration
SUPABASE_URL=https://esxocouzllrxblgeylxu.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVzeG9jb3V6bGxyeGJsZ2V5bHh1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2MjEzNDUsImV4cCI6MjA2NDE5NzM0NX0.s6n3PwcehNSKlJEr-EsHE2JqcE230Dh65YM2hmANmcU
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVzeG9jb3V6bGxyeGJsZ2V5bHh1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYyMTM0NSwiZXhwIjoyMDY0MTk3MzQ1fQ.YeqHKJhZITmCzZ---9oYAktLovNhgdlo_wkP6VOz1WY

# API Configuration
API_BASE_URL=https://esxocouzllrxblgeylxu.supabase.co
API_TIMEOUT=30000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=city_pos_dev
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Firebase Configuration (if using Firebase)
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_API_KEY=your_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_STORAGE_BUCKET=your_project.appspot.com

# Payment Gateway Configuration
PAYMENT_GATEWAY_URL=https://payment.example.com
PAYMENT_GATEWAY_KEY=your_payment_key
PAYMENT_GATEWAY_SECRET=your_payment_secret

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE_PATH=logs/app.log

# Development Configuration
DEBUG_MODE=true
ENABLE_LOGGING=true

# Security Configuration
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# Third-party Services
GOOGLE_MAPS_API_KEY=your_google_maps_key
ANALYTICS_TRACKING_ID=your_analytics_id

# Local Development
LOCAL_SERVER_PORT=8080
LOCAL_SERVER_HOST=localhost
