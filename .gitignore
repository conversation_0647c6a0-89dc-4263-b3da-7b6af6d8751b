# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
.packages
.package_config.json

# Web related
/web/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Temporary folders
tmp/
temp/

# OS generated files
Thumbs.db
ehthumbs.db

# Backup files
*.bak
*.backup
*.tmp
*~

# IDE files
*.sublime-project
*.sublime-workspace

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# FVM Version Cache
.fvm/

# Local configuration file (sdk path, etc)
local.properties

# Keystore files
*.jks
*.keystore
key.properties

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart

# Flutter generated files
lib/generated/
**/generated_plugin_registrant.dart

# Pods (iOS/macOS)
**/Pods/
**/Podfile.lock

# Build outputs
**/build/
**/out/
