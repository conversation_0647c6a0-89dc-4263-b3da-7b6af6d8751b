# Contributing to City POS

Thank you for your interest in contributing to City POS! This document provides guidelines and instructions for contributors.

## 🚀 Getting Started

### Development Environment Setup

1. **Install Required Tools**
   - Flutter SDK 3.32.0 (use the exact version in `.flutter-version`)
   - Dart SDK 3.8.0
   - Git
   - VS Code or Android Studio

2. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd city_pos
   flutter pub get
   cp .env.example .env
   ```

3. **Verify Setup**
   ```bash
   flutter doctor
   flutter test
   flutter analyze
   ```

## 📋 Development Workflow

### Branch Strategy
- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/feature-name` - Feature development
- `bugfix/bug-description` - Bug fixes
- `hotfix/critical-fix` - Critical production fixes

### Making Changes

1. **Create Feature Branch**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/your-feature-name
   ```

2. **Development Process**
   - Write code following our style guidelines
   - Add/update tests for your changes
   - Update documentation if needed
   - Commit frequently with clear messages

3. **Before Submitting**
   ```bash
   # Format code
   dart format .
   
   # Run analysis
   flutter analyze
   
   # Run tests
   flutter test
   
   # Check for any issues
   flutter doctor
   ```

4. **Submit Pull Request**
   - Push your branch to remote
   - Create PR against `develop` branch
   - Fill out PR template completely
   - Request review from team members

## 📝 Code Style Guidelines

### Dart/Flutter Conventions
- Use 2 spaces for indentation
- Maximum line length: 80 characters
- Use meaningful variable and function names
- Add documentation comments for public APIs
- Follow Dart naming conventions

### File Organization
```
lib/
├── main.dart
├── app/                  # App-level configuration
├── core/                 # Core utilities, constants
├── features/             # Feature-based modules
│   └── feature_name/
│       ├── data/         # Data layer
│       ├── domain/       # Business logic
│       └── presentation/ # UI layer
├── shared/               # Shared components
└── utils/                # Utility functions
```

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

Examples:
- `feat(auth): add login functionality`
- `fix(payment): resolve payment gateway timeout`
- `docs(readme): update setup instructions`

## 🧪 Testing Guidelines

### Test Types
1. **Unit Tests** - Test individual functions/classes
2. **Widget Tests** - Test UI components
3. **Integration Tests** - Test complete user flows

### Writing Tests
- Write tests for all new features
- Maintain test coverage above 80%
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)

### Running Tests
```bash
# All tests
flutter test

# Specific test file
flutter test test/features/auth/auth_test.dart

# With coverage
flutter test --coverage
```

## 🔍 Code Review Process

### For Authors
- Ensure CI passes before requesting review
- Provide clear PR description
- Respond to feedback promptly
- Keep PRs focused and reasonably sized

### For Reviewers
- Review within 24 hours
- Provide constructive feedback
- Check for code style, logic, and tests
- Approve only when confident in changes

## 🐛 Bug Reports

### Before Reporting
- Check existing issues
- Verify bug on latest version
- Test on multiple devices/platforms

### Bug Report Template
```markdown
**Description**
Clear description of the bug

**Steps to Reproduce**
1. Step one
2. Step two
3. Step three

**Expected Behavior**
What should happen

**Actual Behavior**
What actually happens

**Environment**
- Flutter version:
- Dart version:
- Platform:
- Device:

**Screenshots**
If applicable
```

## 🚀 Feature Requests

### Before Requesting
- Check if feature already exists
- Search existing feature requests
- Consider if it fits project scope

### Feature Request Template
```markdown
**Feature Description**
Clear description of the feature

**Use Case**
Why is this feature needed?

**Proposed Solution**
How should it work?

**Alternatives Considered**
Other approaches considered

**Additional Context**
Any other relevant information
```

## 📚 Documentation

### Code Documentation
- Document all public APIs
- Use clear, concise comments
- Include examples where helpful
- Keep documentation up-to-date

### README Updates
- Update setup instructions for new requirements
- Document new features
- Keep troubleshooting section current

## 🔒 Security

### Reporting Security Issues
- Do NOT create public issues for security vulnerabilities
- Email security concerns to: [security-email]
- Include detailed description and steps to reproduce

### Security Guidelines
- Never commit secrets or API keys
- Use environment variables for sensitive data
- Follow secure coding practices
- Validate all user inputs

## 📞 Getting Help

### Communication Channels
- Team Slack: #city-pos-dev
- Email: [team-email]
- Weekly standup: [time/day]

### Resources
- [Flutter Documentation](https://docs.flutter.dev/)
- [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- [Project Wiki](link-to-wiki)

## 📄 License

By contributing to City POS, you agree that your contributions will be licensed under the same license as the project.

---

Thank you for contributing to City POS! 🎉
