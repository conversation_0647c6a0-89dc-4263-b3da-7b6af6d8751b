{"@@locale": "en", "appName": "City POS", "@appName": {"description": "The name of the application"}, "appDescription": "Point of Sale Management System", "@appDescription": {"description": "Brief description of the application"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "register": "Register", "@register": {"description": "Register button text"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "fullName": "Full Name", "@fullName": {"description": "Full name field label"}, "enterYourAccountInfo": "Enter your account information", "@enterYourAccountInfo": {"description": "Instruction to enter account information"}, "phone": "Phone Number", "@phone": {"description": "Phone number field label"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Text asking if user doesn't have an account"}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {"description": "Text asking if user already has an account"}, "registerNow": "Register now", "@registerNow": {"description": "Register now link text"}, "signInNow": "Sign in now", "@signInNow": {"description": "Sign in now link text"}, "rememberMe": "Remember me", "@rememberMe": {"description": "Remember me checkbox text"}, "dashboard": "Dashboard", "@dashboard": {"description": "Dashboard menu item"}, "welcome": "Welcome back!", "@welcome": {"description": "Welcome message for returning users"}, "today": "Today is", "@today": {"description": "Today's date prefix"}, "quickStats": "Quick Stats", "@quickStats": {"description": "Quick statistics section title"}, "totalSales": "Total Sales", "@totalSales": {"description": "Total sales metric"}, "totalOrders": "Total Orders", "@totalOrders": {"description": "Total orders metric"}, "totalCustomers": "Total Customers", "@totalCustomers": {"description": "Total customers metric"}, "totalProducts": "Total Products", "@totalProducts": {"description": "Total products count"}, "recentOrders": "Recent Orders", "@recentOrders": {"description": "Recent orders section title"}, "viewAll": "View All", "@viewAll": {"description": "View all items link text"}, "noOrdersYet": "No orders yet", "@noOrdersYet": {"description": "Message when there are no orders"}, "startSelling": "Start selling to see orders here", "@startSelling": {"description": "Instruction to start selling"}, "pos": "POS", "@pos": {"description": "Point of Sale menu item"}, "products": "Products", "@products": {"description": "Products menu item"}, "inventory": "Inventory", "@inventory": {"description": "Inventory menu item"}, "orders": "Orders", "@orders": {"description": "Orders menu item"}, "customers": "Customers", "@customers": {"description": "Customers menu item"}, "partners": "Partners", "@partners": {"description": "Partners menu item"}, "finance": "Finance", "@finance": {"description": "Finance menu item"}, "cashbook": "Cashbook", "@cashbook": {"description": "Cashbook menu item"}, "invoices": "Invoices", "@invoices": {"description": "Invoices menu item"}, "reports": "Reports", "@reports": {"description": "Reports menu item"}, "settings": "Settings", "@settings": {"description": "Settings menu item"}, "profile": "Profile", "@profile": {"description": "Profile menu item"}, "users": "Users", "@users": {"description": "Users menu item"}, "language": "Language", "@language": {"description": "Language setting label"}, "vietnamese": "Tiếng <PERSON>", "@vietnamese": {"description": "Vietnamese language option"}, "english": "English", "@english": {"description": "English language option"}, "japanese": "日本語", "@japanese": {"description": "Japanese language option"}, "changeLanguage": "Change Language", "@changeLanguage": {"description": "Change language button text"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Select language dialog title"}, "languageChangedToVietnamese": "Language changed to Vietnamese", "@languageChangedToVietnamese": {"description": "Confirmation message when language changed to Vietnamese"}, "languageChangedToEnglish": "Language changed to English", "@languageChangedToEnglish": {"description": "Confirmation message when language changed to English"}, "languageChangedToJapanese": "Language changed to Japanese", "@languageChangedToJapanese": {"description": "Confirmation message when language changed to Japanese"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "add": "Add", "@add": {"description": "Add button text"}, "search": "Search", "@search": {"description": "Search button text"}, "filter": "Filter", "@filter": {"description": "Filter button text"}, "sort": "Sort", "@sort": {"description": "Sort button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "success": "Success", "@success": {"description": "Success message type"}, "error": "Error", "@error": {"description": "Error message type"}, "warning": "Warning", "@warning": {"description": "Warning message type"}, "info": "Information", "@info": {"description": "Information message type"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "noData": "No data available", "@noData": {"description": "No data available message"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "name": "Name", "@name": {"description": "Name field label"}, "description": "Description", "@description": {"description": "Description field label"}, "price": "Price", "@price": {"description": "Price field label"}, "quantity": "Quantity", "@quantity": {"description": "Quantity field label"}, "total": "Total", "@total": {"description": "Total field label"}, "subtotal": "Subtotal", "@subtotal": {"description": "Subtotal field label"}, "tax": "Tax", "@tax": {"description": "Tax field label"}, "discount": "Discount", "@discount": {"description": "Discount field label"}, "amount": "Amount", "@amount": {"description": "Amount field label"}, "date": "Date", "@date": {"description": "Date field label"}, "time": "Time", "@time": {"description": "Time field label"}, "status": "Status", "@status": {"description": "Status field label"}, "category": "Category", "@category": {"description": "Category field label"}, "type": "Type", "@type": {"description": "Type field label"}, "code": "Code", "@code": {"description": "Code field label"}, "address": "Address", "@address": {"description": "Address field label"}, "notes": "Notes", "@notes": {"description": "Notes field label"}, "active": "Active", "@active": {"description": "Active status"}, "inactive": "Inactive", "@inactive": {"description": "Inactive status"}, "enabled": "Enabled", "@enabled": {"description": "Enabled status"}, "disabled": "Disabled", "@disabled": {"description": "Disabled status"}, "available": "Available", "@available": {"description": "Available status"}, "unavailable": "Unavailable", "@unavailable": {"description": "Unavailable status"}, "inStock": "In Stock", "@inStock": {"description": "In stock status"}, "outOfStock": "Out of Stock", "@outOfStock": {"description": "Out of stock status"}, "lowStock": "Low Stock", "@lowStock": {"description": "Low stock status"}, "cash": "Cash", "@cash": {"description": "Cash payment method"}, "card": "Card", "@card": {"description": "Card payment method"}, "bankTransfer": "Bank Transfer", "@bankTransfer": {"description": "Bank transfer payment method"}, "eWallet": "E-Wallet", "@eWallet": {"description": "E-wallet payment method"}, "creditCard": "Credit Card", "@creditCard": {"description": "Credit card payment method"}, "debitCard": "Debit Card", "@debitCard": {"description": "Debit card payment method"}, "customer": "Customer", "@customer": {"description": "Customer label"}, "supplier": "Supplier", "@supplier": {"description": "Supplier label"}, "partner": "Partner", "@partner": {"description": "Partner label"}, "vendor": "<PERSON><PERSON><PERSON>", "@vendor": {"description": "Vendor label"}, "income": "Income", "@income": {"description": "Income type"}, "expense": "Expense", "@expense": {"description": "Expense type"}, "receipt": "Receipt", "@receipt": {"description": "Receipt type"}, "payment": "Payment", "@payment": {"description": "Payment type"}, "draft": "Draft", "@draft": {"description": "Draft status"}, "pending": "Pending", "@pending": {"description": "Pending status"}, "approved": "Approved", "@approved": {"description": "Approved status"}, "rejected": "Rejected", "@rejected": {"description": "Rejected status"}, "completed": "Completed", "@completed": {"description": "Completed status"}, "cancelled": "Cancelled", "@cancelled": {"description": "Cancelled status"}, "paid": "Paid", "@paid": {"description": "Paid status"}, "unpaid": "Unpaid", "@unpaid": {"description": "Unpaid status"}, "overdue": "Overdue", "@overdue": {"description": "Overdue status"}, "sent": "<PERSON><PERSON>", "@sent": {"description": "Sent status"}, "unit": "Unit", "@unit": {"description": "Unit label"}, "piece": "Piece", "@piece": {"description": "Piece unit"}, "kilogram": "Kilogram", "@kilogram": {"description": "Kilogram unit"}, "gram": "Gram", "@gram": {"description": "Gram unit"}, "liter": "Liter", "@liter": {"description": "Liter unit"}, "meter": "<PERSON>er", "@meter": {"description": "Meter unit"}, "box": "Box", "@box": {"description": "Box unit"}, "pack": "Pack", "@pack": {"description": "Pack unit"}, "required": "Required", "@required": {"description": "Required field indicator"}, "optional": "Optional", "@optional": {"description": "Optional field indicator"}, "pleaseEnter": "Please enter", "@pleaseEnter": {"description": "Please enter validation message"}, "pleaseSelect": "Please select", "@pleaseSelect": {"description": "Please select validation message"}, "invalidFormat": "Invalid format", "@invalidFormat": {"description": "Invalid format validation message"}, "tooShort": "Too short", "@tooShort": {"description": "Too short validation message"}, "tooLong": "Too long", "@tooLong": {"description": "Too long validation message"}, "mustBeNumber": "Must be a number", "@mustBeNumber": {"description": "Must be number validation message"}, "mustBePositive": "Must be positive", "@mustBePositive": {"description": "Must be positive validation message"}, "emailInvalid": "Invalid email format", "@emailInvalid": {"description": "Invalid email validation message"}, "phoneInvalid": "Invalid phone format", "@phoneInvalid": {"description": "Invalid phone validation message"}, "passwordTooShort": "Password too short", "@passwordTooShort": {"description": "Password too short validation message"}, "passwordMismatch": "Passwords do not match", "@passwordMismatch": {"description": "Password mismatch validation message"}, "deleteConfirmation": "Are you sure you want to delete this item?", "@deleteConfirmation": {"description": "Delete confirmation message"}, "deleteSuccess": "Item deleted successfully", "@deleteSuccess": {"description": "Delete success message"}, "deleteError": "Failed to delete item", "@deleteError": {"description": "Delete error message"}, "saveSuccess": "Saved successfully", "@saveSuccess": {"description": "Save success message"}, "saveError": "Failed to save", "@saveError": {"description": "Save error message"}, "updateSuccess": "Updated successfully", "@updateSuccess": {"description": "Update success message"}, "updateError": "Failed to update", "@updateError": {"description": "Update error message"}, "createSuccess": "Created successfully", "@createSuccess": {"description": "Create success message"}, "createError": "Failed to create", "@createError": {"description": "Create error message"}, "loadError": "Failed to load data", "@loadError": {"description": "Load error message"}, "networkError": "Network connection error", "@networkError": {"description": "Network error message"}, "serverError": "Server error", "@serverError": {"description": "Server error message"}, "unknownError": "Unknown error occurred", "@unknownError": {"description": "Unknown error message"}, "loginSuccess": "Login successful", "@loginSuccess": {"description": "Login success message"}, "loginError": "<PERSON><PERSON> failed", "@loginError": {"description": "Login error message"}, "logoutSuccess": "Logout successful", "@logoutSuccess": {"description": "Logout success message"}, "registerSuccess": "Registration successful", "@registerSuccess": {"description": "Registration success message"}, "registerError": "Registration failed", "@registerError": {"description": "Registration error message"}, "invalidCredentials": "Invalid email or password", "@invalidCredentials": {"description": "Invalid credentials error message"}, "accountNotFound": "Account not found", "@accountNotFound": {"description": "Account not found error message"}, "accountDisabled": "Account is disabled", "@accountDisabled": {"description": "Account disabled error message"}, "emailAlreadyExists": "Email already exists", "@emailAlreadyExists": {"description": "Email already exists error message"}, "loginNow": "Login now", "@loginNow": {"description": "Login now link text"}, "quickActions": "Quick Actions", "@quickActions": {"description": "Quick actions section title"}, "todayRevenue": "Today's Revenue", "@todayRevenue": {"description": "Today's revenue metric"}, "todayOrders": "Today's Orders", "@todayOrders": {"description": "Today's orders metric"}, "lowStockProducts": "Low Stock Products", "@lowStockProducts": {"description": "Low stock products metric"}, "sales": "Sales", "@sales": {"description": "Sales menu item"}, "newSale": "New Sale", "@newSale": {"description": "New sale button text"}, "addProduct": "Add Product", "@addProduct": {"description": "Add product button text"}, "stockTransactions": "Stock Transactions", "@stockTransactions": {"description": "Stock transactions menu item"}, "addStock": "Add Stock", "@addStock": {"description": "Add stock button text"}, "stockIn": "Stock In", "@stockIn": {"description": "Stock in transaction type"}, "stockOut": "Stock Out", "@stockOut": {"description": "Stock out transaction type"}, "stockManagement": "Stock Management", "@stockManagement": {"description": "Stock management section title"}, "suppliers": "Suppliers", "@suppliers": {"description": "Suppliers menu item"}, "selectYourPreferredLanguage": "Select your preferred language", "@selectYourPreferredLanguage": {"description": "Language selection instruction"}, "enterEmail": "Enter your email", "@enterEmail": {"description": "Email field placeholder"}, "enterPassword": "Enter your password", "@enterPassword": {"description": "Password field placeholder"}, "enterName": "Enter your name", "@enterName": {"description": "Name field placeholder"}, "enterPhone": "Enter your phone number", "@enterPhone": {"description": "Phone field placeholder"}, "confirmPasswordHint": "Re-enter your password", "@confirmPasswordHint": {"description": "Confirm password field placeholder"}, "emailRequired": "Email is required", "@emailRequired": {"description": "Email required validation message"}, "passwordRequired": "Password is required", "@passwordRequired": {"description": "Password required validation message"}, "nameRequired": "Name is required", "@nameRequired": {"description": "Name required validation message"}, "phoneRequired": "Phone number is required", "@phoneRequired": {"description": "Phone required validation message"}, "invalidEmail": "Invalid email format", "@invalidEmail": {"description": "Invalid email validation message"}, "invalidPhone": "Invalid phone number", "@invalidPhone": {"description": "Invalid phone validation message"}, "loginFailed": "<PERSON><PERSON> failed", "@loginFailed": {"description": "<PERSON><PERSON> failed message"}, "registerFailed": "Registration failed", "@registerFailed": {"description": "Registration failed message"}, "featureInDevelopment": "Feature in development", "@featureInDevelopment": {"description": "Feature in development message"}, "comingSoon": "Coming soon", "@comingSoon": {"description": "Coming soon message"}, "version": "Version", "@version": {"description": "Version label"}, "notifications": "Notifications", "@notifications": {"description": "Notifications menu item"}, "notificationsComingSoon": "Notifications coming soon", "@notificationsComingSoon": {"description": "Notifications coming soon message"}, "profileSettings": "Profile Settings", "@profileSettings": {"description": "Profile settings menu item"}, "profileSettingsComingSoon": "Profile settings coming soon", "@profileSettingsComingSoon": {"description": "Profile settings coming soon message"}, "userManagement": "User Management", "@userManagement": {"description": "User management menu item"}, "userManagementComingSoon": "User management coming soon", "@userManagementComingSoon": {"description": "User management coming soon message"}, "manageUsers": "Manage users", "@manageUsers": {"description": "Manage users action text"}, "manageYourProfile": "Manage your profile", "@manageYourProfile": {"description": "Manage profile action text"}, "areYouSureLogout": "Are you sure you want to logout?", "@areYouSureLogout": {"description": "Logout confirmation message"}, "signOut": "Sign out", "@signOut": {"description": "Sign out button text"}, "signOutOfYourAccount": "Sign out of your account", "@signOutOfYourAccount": {"description": "Sign out description text"}, "appInformation": "App Information", "@appInformation": {"description": "App information section title"}, "otherSettings": "Other Settings", "@otherSettings": {"description": "Other settings section title"}, "salesReport": "Sales Report", "@salesReport": {"description": "Sales report menu item"}, "inventoryReport": "Inventory Report", "@inventoryReport": {"description": "Inventory report menu item"}, "financeReport": "Finance Report", "@financeReport": {"description": "Finance report menu item"}, "customerReport": "Customer Report", "@customerReport": {"description": "Customer report menu item"}, "productReport": "Product Report", "@productReport": {"description": "Product report menu item"}, "profitReport": "Profit Report", "@profitReport": {"description": "Profit report menu item"}, "reportWillBeAvailableSoon": "This report will be available soon", "@reportWillBeAvailableSoon": {"description": "Report coming soon message"}, "pointOfSaleManagementSystem": "Point of Sale Management System", "@pointOfSaleManagementSystem": {"description": "Full app description"}, "noOrdersFound": "No orders found", "@noOrdersFound": {"description": "No orders found message"}, "createFirstOrder": "Create your first order to get started", "@createFirstOrder": {"description": "Create first order instruction"}, "analytics": "Analytics", "@analytics": {"description": "Analytics menu item"}, "orderStatusUpdated": "Order status updated", "@orderStatusUpdated": {"description": "Order status updated confirmation"}, "paymentStatusUpdated": "Payment status updated", "@paymentStatusUpdated": {"description": "Payment status updated confirmation"}, "orderDeleted": "Order deleted", "@orderDeleted": {"description": "Order deleted confirmation"}, "deleteOrder": "Delete order", "@deleteOrder": {"description": "Delete order action"}, "confirmDeleteOrder": "Are you sure you want to delete order", "@confirmDeleteOrder": {"description": "Delete order confirmation message"}, "order": "Order", "@order": {"description": "Order label"}, "paymentStatus": "Payment Status", "@paymentStatus": {"description": "Payment status label"}, "items": "items", "@items": {"description": "Items count label"}, "close": "Close", "@close": {"description": "Close button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "complete": "Complete", "@complete": {"description": "Complete button text"}, "markAsPaid": "Mark as paid", "@markAsPaid": {"description": "Mark as paid action"}, "markAsPartial": "Mark as partial payment", "@markAsPartial": {"description": "Mark as partial payment action"}, "salesOverview": "Sales Overview", "@salesOverview": {"description": "Sales overview section title"}, "totalRevenue": "Total Revenue", "@totalRevenue": {"description": "Total revenue"}, "completedOrders": "Completed Orders", "@completedOrders": {"description": "Completed orders metric"}, "pendingOrders": "Pending Orders", "@pendingOrders": {"description": "Pending orders metric"}, "paidAmount": "<PERSON><PERSON>", "@paidAmount": {"description": "Paid amount metric"}, "pendingAmount": "Pending Amount", "@pendingAmount": {"description": "Pending amount metric"}, "averageOrderValue": "Avg Order Value", "@averageOrderValue": {"description": "Average order value"}, "conversionRate": "Conversion Rate", "@conversionRate": {"description": "Conversion rate metric"}, "filters": "Filters", "@filters": {"description": "Filters section label"}, "clearFilters": "Clear filters", "@clearFilters": {"description": "Clear all filters button text"}, "clearFilter": "Clear filter", "@clearFilter": {"description": "Clear single filter button text"}, "orderStatus": "Order Status", "@orderStatus": {"description": "Order status filter label"}, "allStatuses": "All statuses", "@allStatuses": {"description": "All statuses filter option"}, "confirmed": "Confirmed", "@confirmed": {"description": "Confirmed status"}, "allPaymentStatuses": "All payment statuses", "@allPaymentStatuses": {"description": "All payment statuses filter option"}, "pendingPayment": "Pending payment", "@pendingPayment": {"description": "Pending payment status"}, "partialPayment": "Partial payment", "@partialPayment": {"description": "Partial payment status"}, "refunded": "Refunded", "@refunded": {"description": "Refunded status"}, "selectDateRange": "Select Date Range", "@selectDateRange": {"description": "Tooltip for selecting date range"}, "exportReports": "Export reports", "@exportReports": {"description": "Export reports button text"}, "salesReportDescription": "View sales performance and revenue trends", "@salesReportDescription": {"description": "Sales report description text"}, "inventoryReportDescription": "Track stock levels and inventory movements", "@inventoryReportDescription": {"description": "Inventory report description text"}, "financeReportDescription": "Monitor cash flow and financial status", "@financeReportDescription": {"description": "Finance report description text"}, "customerReportDescription": "Analyze customer behavior and preferences", "@customerReportDescription": {"description": "Customer report description text"}, "productReportDescription": "Review product performance and popularity", "@productReportDescription": {"description": "Product report description text"}, "profitReportDescription": "Calculate profit margins and profitability", "@profitReportDescription": {"description": "Profit report description text"}, "reportsComingSoon": "Advanced reports coming soon", "@reportsComingSoon": {"description": "Advanced reports coming soon title"}, "reportsComingSoonDescription": "Detailed analytics and insights will be available in the next update", "@reportsComingSoonDescription": {"description": "Advanced reports coming soon description"}, "yesterday": "Yesterday", "@yesterday": {"description": "Yesterday date filter option"}, "thisWeek": "This week", "@thisWeek": {"description": "This week date filter option"}, "lastWeek": "Last week", "@lastWeek": {"description": "Last week date filter option"}, "thisMonth": "This month", "@thisMonth": {"description": "This month date filter option"}, "lastMonth": "Last month", "@lastMonth": {"description": "Last month date filter option"}, "selectCustomRange": "Select custom range", "@selectCustomRange": {"description": "Custom date range selection option"}, "clearSelection": "Clear selection", "@clearSelection": {"description": "Clear selection button text"}, "noProductsFound": "No products found", "@noProductsFound": {"description": "No products found message"}, "addFirstProduct": "Add your first product to get started", "@addFirstProduct": {"description": "Add first product instruction"}, "searchProducts": "Search products...", "@searchProducts": {"description": "Search products placeholder text"}, "allCategories": "All categories", "@allCategories": {"description": "All categories filter option"}, "productName": "Product Name", "@productName": {"description": "Product name field label"}, "enterProductName": "Enter product name", "@enterProductName": {"description": "Product name field placeholder"}, "pleaseEnterProductName": "Please enter product name", "@pleaseEnterProductName": {"description": "Product name required validation"}, "enterDescription": "Enter description", "@enterDescription": {"description": "Description field placeholder"}, "enterSku": "Enter SKU", "@enterSku": {"description": "SKU field placeholder"}, "barcode": "Barcode", "@barcode": {"description": "Barcode field label"}, "enterBarcode": "Enter barcode", "@enterBarcode": {"description": "Barcode field placeholder"}, "categories": "Categories", "@categories": {"description": "Categories field label"}, "categoryManagement": "Category Management", "@categoryManagement": {"description": "Category management section title"}, "cost": "Cost", "@cost": {"description": "Cost field label"}, "pleaseEnterPrice": "Please enter price", "@pleaseEnterPrice": {"description": "Price required validation"}, "pleaseEnterValidPrice": "Please enter valid price", "@pleaseEnterValidPrice": {"description": "Valid price validation"}, "stockQuantity": "Stock Quantity", "@stockQuantity": {"description": "Stock quantity field label"}, "pleaseEnterStock": "Please enter stock quantity", "@pleaseEnterStock": {"description": "Stock quantity required validation"}, "pleaseEnterValidStock": "Please enter valid stock quantity", "@pleaseEnterValidStock": {"description": "Valid stock quantity validation"}, "minStock": "Minimum Stock", "@minStock": {"description": "Minimum stock field label"}, "maxStock": "Maximum Stock", "@maxStock": {"description": "Maximum stock field label"}, "productActiveDescription": "Product can be sold", "@productActiveDescription": {"description": "Product active status description"}, "update": "Update", "@update": {"description": "Update button text"}, "deleteProduct": "Delete product", "@deleteProduct": {"description": "Delete product action"}, "confirmDeleteProduct": "Are you sure you want to delete this product?", "@confirmDeleteProduct": {"description": "Delete product confirmation message"}, "productDeleted": "Product deleted", "@productDeleted": {"description": "Product deleted confirmation"}, "errorOccurred": "An error occurred", "@errorOccurred": {"description": "Generic error message"}, "createAccount": "Create Account", "@createAccount": {"description": "Create account button text"}, "enterAccountDetails": "Enter account details", "@enterAccountDetails": {"description": "Enter account details instruction"}, "enterFullName": "Enter full name", "@enterFullName": {"description": "Full name field placeholder"}, "phoneNumber": "Phone Number", "@phoneNumber": {"description": "Phone number field label"}, "enterPhoneNumber": "Enter phone number", "@enterPhoneNumber": {"description": "Phone number field placeholder"}, "editProduct": "Edit Product", "@editProduct": {"description": "Edit product screen title"}, "sku": "SKU", "@sku": {"description": "SKU field label"}, "addNewPartner": "Add New Partner", "@addNewPartner": {"description": "Add new partner screen title"}, "savePartner": "Save Partner", "@savePartner": {"description": "Save partner button text"}, "partnerAddedSuccessfully": "Partner added successfully", "@partnerAddedSuccessfully": {"description": "Partner added success message"}, "errorAddingPartner": "Error adding partner", "@errorAddingPartner": {"description": "Error adding partner message"}, "customerType": "Customer", "@customerType": {"description": "Customer partner type"}, "supplierType": "Supplier", "@supplierType": {"description": "Supplier partner type"}, "addFinanceTransaction": "Add Finance Transaction", "@addFinanceTransaction": {"description": "Add finance transaction screen title"}, "saveTransaction": "Save Transaction", "@saveTransaction": {"description": "Save transaction button text"}, "transactionDescription": "Description", "@transactionDescription": {"description": "Transaction description label"}, "transactionDescriptionHint": "e.g. Sales, Purchase materials", "@transactionDescriptionHint": {"description": "Transaction description hint"}, "pleaseEnterDescription": "Please enter description", "@pleaseEnterDescription": {"description": "Please enter description validation"}, "incomeType": "Income", "@incomeType": {"description": "Income transaction type"}, "expenseType": "Expense", "@expenseType": {"description": "Expense transaction type"}, "allProducts": "All", "@allProducts": {"description": "All products tab"}, "drinks": "Drinks", "@drinks": {"description": "Drinks category"}, "food": "Food", "@food": {"description": "Food category"}, "snacks": "Snacks", "@snacks": {"description": "Snacks category"}, "other": "Other", "@other": {"description": "Other category"}, "searchProductHint": "Search products by name or barcode...", "@searchProductHint": {"description": "Search product hint text"}, "barcodeFeatureInDevelopment": "Barcode scanning feature is in development", "@barcodeFeatureInDevelopment": {"description": "Barcode feature in development message"}, "emptyCart": "Cart is empty", "@emptyCart": {"description": "Empty cart message"}, "addProductsToStart": "Add products to get started", "@addProductsToStart": {"description": "Add products to cart instruction"}, "invoiceStatistics": "Invoice Statistics", "@invoiceStatistics": {"description": "Invoice statistics title"}, "noInvoicesYet": "No invoices yet", "@noInvoicesYet": {"description": "No invoices message"}, "createFirstInvoice": "Create your first invoice to get started", "@createFirstInvoice": {"description": "Create first invoice instruction"}, "noCategoriesYet": "No categories yet", "@noCategoriesYet": {"description": "No categories message"}, "noCategoriesFound": "No categories found", "@noCategoriesFound": {"description": "No categories found message"}, "addFirstCategory": "Add your first category to get started", "@addFirstCategory": {"description": "Add first category instruction"}, "tryDifferentKeyword": "Try searching with different keywords", "@tryDifferentKeyword": {"description": "Try different keyword suggestion"}, "addCategory": "Add Category", "@addCategory": {"description": "Add category button text"}, "editCategory": "Edit Category", "@editCategory": {"description": "Edit category title"}, "categoryName": "Category Name", "@categoryName": {"description": "Category name label"}, "categoryDescription": "Description", "@categoryDescription": {"description": "Category description label"}, "addStockTransaction": "Add Stock Transaction", "@addStockTransaction": {"description": "Add stock transaction screen title"}, "salesCategories": "Sales", "@salesCategories": {"description": "Sales category"}, "serviceCategories": "Service", "@serviceCategories": {"description": "Service category"}, "interestCategories": "Interest", "@interestCategories": {"description": "Interest category"}, "purchaseCategories": "Purchase", "@purchaseCategories": {"description": "Purchase category"}, "salaryCategories": "Salary", "@salaryCategories": {"description": "Salary category"}, "rentCategories": "Rent", "@rentCategories": {"description": "Rent category"}, "utilitiesCategories": "Utilities", "@utilitiesCategories": {"description": "Utilities category"}, "marketingCategories": "Marketing", "@marketingCategories": {"description": "Marketing category"}, "noNotifications": "No notifications", "@noNotifications": {"description": "No notifications message"}, "allNotificationsWillAppearHere": "All notifications will appear here", "@allNotificationsWillAppearHere": {"description": "Notifications instruction message"}, "stockTransactionsTitle": "Stock Transactions", "@stockTransactionsTitle": {"description": "Stock transactions screen title"}, "cart": "<PERSON><PERSON>", "@cart": {"description": "Cart label"}, "clearAll": "Clear all", "@clearAll": {"description": "Clear all button"}, "invoiceList": "Invoice List", "@invoiceList": {"description": "Invoice list title"}, "invoicesCount": "invoices", "@invoicesCount": {"description": "Invoice count unit"}, "addReceiptExpense": "Add receipt/expense", "@addReceiptExpense": {"description": "Add receipt expense tooltip"}, "weeklyRevenueReport": "Weekly Revenue Report", "@weeklyRevenueReport": {"description": "Weekly revenue report title"}, "weeklyRevenueReportReady": "This week's revenue report is ready to view", "@weeklyRevenueReportReady": {"description": "Weekly revenue report content"}, "viewReport": "View report", "@viewReport": {"description": "View report button label"}, "blackCoffee": "Black Coffee", "@blackCoffee": {"description": "Black coffee product name"}, "freshMilk": "Fresh Milk", "@freshMilk": {"description": "Fresh milk product name"}, "customerName": "<PERSON>", "@customerName": {"description": "Sample customer name"}, "rememberLogin": "Remember login", "@rememberLogin": {"description": "Remember login checkbox label"}, "alreadyHaveAccountLogin": "Already have an account? ", "@alreadyHaveAccountLogin": {"description": "Already have account text"}, "loginNowLink": "Login now", "@loginNowLink": {"description": "Login now link"}, "posTitle": "POS - Sales", "@posTitle": {"description": "POS screen title"}, "cartTitle": "<PERSON><PERSON>", "@cartTitle": {"description": "Cart title with count"}, "clearAllTooltip": "Clear all", "@clearAllTooltip": {"description": "Clear all button tooltip"}, "invoiceListTitle": "Invoice List", "@invoiceListTitle": {"description": "Invoice list title"}, "invoicesCountLabel": "invoices", "@invoicesCountLabel": {"description": "Invoice count label"}, "financeTitle": "Cashbook", "@financeTitle": {"description": "Finance screen title"}, "markAllAsRead": "Mark all as read", "@markAllAsRead": {"description": "Mark all notifications as read button"}, "deleteAllRead": "Delete all read", "@deleteAllRead": {"description": "Delete all read notifications button"}, "notificationSettings": "Notification settings", "@notificationSettings": {"description": "Notification settings button"}, "deleteAllReadConfirm": "Delete all read notifications", "@deleteAllReadConfirm": {"description": "Delete notifications confirmation title"}, "deleteAllReadMessage": "Are you sure you want to delete all read notifications?", "@deleteAllReadMessage": {"description": "Delete notifications confirmation message"}, "notificationSettingsTitle": "Notification Settings", "@notificationSettingsTitle": {"description": "Notification settings title"}, "notificationSettingsMessage": "Notification settings feature will be available soon.", "@notificationSettingsMessage": {"description": "Notification settings message"}, "productsCount": "products", "@productsCount": {"description": "Products count word (plural)"}, "viewCart": "View cart", "@viewCart": {"description": "View cart button"}, "clearCart": "Clear cart", "@clearCart": {"description": "Clear cart tooltip"}, "selectDate": "Select date", "@selectDate": {"description": "Select date button"}, "transactionDate": "Transaction date", "@transactionDate": {"description": "Transaction date label"}, "itemTotal": "Item total", "@itemTotal": {"description": "Item total label"}, "noInvoicesFound": "No invoices found", "@noInvoicesFound": {"description": "No invoices message"}, "createFirstInvoiceToStart": "Create your first invoice to get started", "@createFirstInvoiceToStart": {"description": "Create first invoice instruction"}, "posScreenTitle": "Sales (POS)", "@posScreenTitle": {"description": "POS screen title"}, "posScreenEnhancedTitle": "Sales (POS) - Enhanced", "@posScreenEnhancedTitle": {"description": "POS Enhanced screen title"}, "clearCartTooltip": "Clear cart", "@clearCartTooltip": {"description": "Clear cart tooltip"}, "scanBarcode": "Scan barcode", "@scanBarcode": {"description": "Scan barcode tooltip"}, "blackCoffeeDemo": "Black Coffee", "@blackCoffeeDemo": {"description": "Demo black coffee product name"}, "freshMilkDemo": "Fresh Milk", "@freshMilkDemo": {"description": "Demo fresh milk product name"}, "customerNameDemo": "<PERSON>", "@customerNameDemo": {"description": "Demo customer name"}, "weeklyRevenueReportTitle": "Weekly Revenue Report", "@weeklyRevenueReportTitle": {"description": "Weekly revenue report title"}, "weeklyRevenueReportMessage": "This week's revenue report is ready to view", "@weeklyRevenueReportMessage": {"description": "Weekly revenue report message"}, "issueDate": "Date", "@issueDate": {"description": "Issue date label"}, "dueDate": "Due", "@dueDate": {"description": "Due date label"}, "paidAmountShort": "Paid", "@paidAmountShort": {"description": "Paid amount label (short)"}, "remainingAmount": "Remaining", "@remainingAmount": {"description": "Remaining amount label"}, "statusDraft": "Draft", "@statusDraft": {"description": "Draft status"}, "statusSent": "<PERSON><PERSON>", "@statusSent": {"description": "Sent status"}, "statusPaid": "Paid", "@statusPaid": {"description": "Paid status"}, "statusOverdue": "Overdue", "@statusOverdue": {"description": "Overdue status"}, "statusCancelled": "Cancelled", "@statusCancelled": {"description": "Cancelled status"}, "cashFlowDetailTitle": "Cash Flow Detail", "@cashFlowDetailTitle": {"description": "Cash flow detail screen title"}, "editEntry": "Edit", "@editEntry": {"description": "Edit entry button"}, "deleteEntry": "Delete", "@deleteEntry": {"description": "Delete entry button"}, "saveChanges": "Save Changes", "@saveChanges": {"description": "Save changes button"}, "cancelEdit": "Cancel", "@cancelEdit": {"description": "Cancel edit button"}, "entryUpdatedSuccessfully": "Entry updated successfully", "@entryUpdatedSuccessfully": {"description": "Entry updated success message"}, "errorUpdatingEntry": "Error updating entry", "@errorUpdatingEntry": {"description": "Error updating entry message"}, "confirmDeleteEntry": "Confirm Delete Entry", "@confirmDeleteEntry": {"description": "Confirm delete entry title"}, "deleteEntryMessage": "Are you sure you want to delete this entry?", "@deleteEntryMessage": {"description": "Delete entry confirmation message"}, "entryDeletedSuccessfully": "Entry deleted successfully", "@entryDeletedSuccessfully": {"description": "Entry deleted success message"}, "errorDeletingEntry": "Error deleting entry", "@errorDeletingEntry": {"description": "Error deleting entry message"}, "stockTransactionsScreen": "Stock Transactions", "@stockTransactionsScreen": {"description": "Stock transactions screen title"}, "addStockTransactionButton": "Add Stock Transaction", "@addStockTransactionButton": {"description": "Add stock transaction button"}, "stockInType": "Stock In", "@stockInType": {"description": "Stock in type"}, "stockOutType": "Stock Out", "@stockOutType": {"description": "Stock out type"}, "stockAdjustmentType": "Stock Adjustment", "@stockAdjustmentType": {"description": "Stock adjustment type"}, "transactionType": "Transaction Type", "@transactionType": {"description": "Transaction type label"}, "reason": "Reason", "@reason": {"description": "Reason label"}, "reference": "Reference", "@reference": {"description": "Reference label"}, "noTransactionsFound": "No transactions found", "@noTransactionsFound": {"description": "No transactions message"}, "createFirstTransaction": "Create your first transaction to get started", "@createFirstTransaction": {"description": "Create first transaction instruction"}, "totalTransactions": "Total Transactions", "@totalTransactions": {"description": "Total transactions label"}, "stockFilters": "Stock Filters", "@stockFilters": {"description": "Stock filters title"}, "transactionTypeFilter": "Transaction Type", "@transactionTypeFilter": {"description": "Transaction type filter label"}, "allTransactionTypes": "All", "@allTransactionTypes": {"description": "All transaction types option"}, "stockDateRange": "Date Range", "@stockDateRange": {"description": "Stock date range label"}, "stockStartDate": "From Date", "@stockStartDate": {"description": "Stock start date label"}, "stockEndDate": "To Date", "@stockEndDate": {"description": "Stock end date label"}, "stockQuickFilters": "Quick Filters", "@stockQuickFilters": {"description": "Stock quick filters label"}, "stockToday": "Today", "@stockToday": {"description": "Stock today option"}, "stockThisWeek": "This Week", "@stockThisWeek": {"description": "Stock this week option"}, "stockThisMonth": "This Month", "@stockThisMonth": {"description": "Stock this month option"}, "stockClearFilters": "Clear Filters", "@stockClearFilters": {"description": "Stock clear filters button"}, "createStockTransaction": "Create Stock Transaction", "@createStockTransaction": {"description": "Create stock transaction title"}, "selectTransactionType": "Select Transaction Type", "@selectTransactionType": {"description": "Select transaction type label"}, "partnerName": "Partner Name", "@partnerName": {"description": "Partner name label"}, "enterPartnerName": "Enter partner name (optional)", "@enterPartnerName": {"description": "Partner name placeholder"}, "note": "Note", "@note": {"description": "Note label"}, "enterNote": "Enter note (optional)", "@enterNote": {"description": "Note placeholder"}, "stockProducts": "Products", "@stockProducts": {"description": "Stock products label"}, "addStockProduct": "Add Product", "@addStockProduct": {"description": "Add stock product button"}, "stockProductName": "Product Name", "@stockProductName": {"description": "Stock product name label"}, "enterStockProductName": "Enter product name", "@enterStockProductName": {"description": "Stock product name placeholder"}, "unitPrice": "Unit Price", "@unitPrice": {"description": "Unit price label"}, "enterUnitPrice": "Enter unit price", "@enterUnitPrice": {"description": "Unit price placeholder"}, "enterQuantity": "Enter quantity", "@enterQuantity": {"description": "Quantity placeholder"}, "pleaseAddAtLeastOneStockProduct": "Please add at least one product", "@pleaseAddAtLeastOneStockProduct": {"description": "Add product validation message"}, "pleaseEnterStockProductName": "Please enter product name", "@pleaseEnterStockProductName": {"description": "Product name validation message"}, "pleaseEnterValidStockQuantity": "Please enter valid quantity", "@pleaseEnterValidStockQuantity": {"description": "Quantity validation message"}, "pleaseEnterValidStockPrice": "Please enter valid price", "@pleaseEnterValidStockPrice": {"description": "Price validation message"}, "noStockTransactionsYet": "No stock transactions yet", "@noStockTransactionsYet": {"description": "No stock transactions message"}, "createFirstStockTransaction": "Create your first transaction to get started", "@createFirstStockTransaction": {"description": "Create first stock transaction instruction"}, "moreItems": "more items", "@moreItems": {"description": "More items label"}, "transactionInfo": "Transaction Information", "@transactionInfo": {"description": "Transaction information title"}, "transactionNumber": "Transaction Number", "@transactionNumber": {"description": "Transaction number label"}, "transactionTypeLabel": "Transaction Type", "@transactionTypeLabel": {"description": "Transaction type label"}, "createdDate": "Created Date", "@createdDate": {"description": "Created date label"}, "cartWithCount": "<PERSON><PERSON>", "@cartWithCount": {"description": "Cart title with count"}, "emptyCartMessage": "Cart is empty", "@emptyCartMessage": {"description": "Empty cart message"}, "selectProductsToAddToCart": "Select products to add to cart", "@selectProductsToAddToCart": {"description": "Select products instruction"}, "addStockTransactionTitle": "Add Stock Transaction", "@addStockTransactionTitle": {"description": "Add stock transaction title"}, "saveStockTransaction": "Save Transaction", "@saveStockTransaction": {"description": "Save stock transaction button"}, "transactionCreatedSuccessfully": "Stock transaction created successfully", "@transactionCreatedSuccessfully": {"description": "Transaction created success message"}, "errorCreatingTransaction": "Error creating transaction", "@errorCreatingTransaction": {"description": "Error creating transaction message"}, "stockInRadio": "Stock In", "@stockInRadio": {"description": "Stock in radio button"}, "stockOutRadio": "Stock Out", "@stockOutRadio": {"description": "Stock out radio button"}, "posSearchProducts": "Search products", "@posSearchProducts": {"description": "POS search products label"}, "posSearchProductsHint": "Enter name, product code...", "@posSearchProductsHint": {"description": "POS search products placeholder"}, "posProductCode": "Code", "@posProductCode": {"description": "POS product code label"}, "posStockQuantityShort": "QTY", "@posStockQuantityShort": {"description": "POS stock quantity short label"}, "posNoProductsFound": "No products found", "@posNoProductsFound": {"description": "POS no products found message"}, "posNoProductsYet": "No products yet", "@posNoProductsYet": {"description": "POS no products yet message"}, "posSubtotal": "Subtotal", "@posSubtotal": {"description": "POS subtotal label"}, "posTax": "Tax (10%)", "@posTax": {"description": "POS tax label"}, "posDiscount": "Discount", "@posDiscount": {"description": "POS discount label"}, "posGrandTotal": "Total", "@posGrandTotal": {"description": "POS grand total label"}, "posSaveDraft": "Save Draft", "@posSaveDraft": {"description": "POS save draft button"}, "posCheckout": "Checkout", "@posCheckout": {"description": "POS checkout button"}, "posDraftSaved": "Draft order saved", "@posDraftSaved": {"description": "POS draft saved success message"}, "posScanBarcodeTitle": "Scan Barcode", "@posScanBarcodeTitle": {"description": "POS scan barcode title"}, "posEnterBarcode": "Enter barcode", "@posEnterBarcode": {"description": "POS enter barcode label"}, "posScanOrEnterBarcode": "Scan or enter product code...", "@posScanOrEnterBarcode": {"description": "POS scan or enter barcode placeholder"}, "posUseCameraToScan": "Or use camera to scan barcode", "@posUseCameraToScan": {"description": "POS use camera instruction"}, "addStockTransactionScreenTitle": "Add Stock Transaction", "@addStockTransactionScreenTitle": {"description": "Add stock transaction screen title"}, "saveButton": "Save", "@saveButton": {"description": "Save button"}, "cancelButton": "Cancel", "@cancelButton": {"description": "Cancel button"}, "saveTransactionButton": "Save Transaction", "@saveTransactionButton": {"description": "Save transaction button"}, "stockInLabel": "Stock In", "@stockInLabel": {"description": "Stock in label"}, "stockOutLabel": "Stock Out", "@stockOutLabel": {"description": "Stock out label"}, "stockTransactionCreatedSuccess": "Stock transaction created successfully", "@stockTransactionCreatedSuccess": {"description": "Stock transaction created success message"}, "errorCreatingStockTransaction": "Error creating transaction", "@errorCreatingStockTransaction": {"description": "Error creating transaction message"}, "createStockTransactionDialogTitle": "Create Stock Transaction", "@createStockTransactionDialogTitle": {"description": "Create stock transaction dialog title"}, "createTransactionButton": "Create Transaction", "@createTransactionButton": {"description": "Create transaction button"}, "paymentTitle": "Payment", "@paymentTitle": {"description": "Payment title"}, "enterAmountReceived": "Enter amount received", "@enterAmountReceived": {"description": "Enter amount received placeholder"}, "paymentFeatureInDevelopment": "Payment feature is under development", "@paymentFeatureInDevelopment": {"description": "Payment feature in development message"}, "closeButton": "Close", "@closeButton": {"description": "Close button"}, "completeButton": "Complete", "@completeButton": {"description": "Complete button"}, "orderCreatedSuccess": "Order created successfully", "@orderCreatedSuccess": {"description": "Order created success message"}, "demoProduct1": "Demo Product 1", "@demoProduct1": {"description": "Demo product 1 name"}, "demoProduct2": "Demo Product 2", "@demoProduct2": {"description": "Demo product 2 name"}, "demoProduct3": "Demo Product 3", "@demoProduct3": {"description": "Demo product 3 name"}, "productSectionTitle": "Product", "@productSectionTitle": {"description": "Product section title"}, "selectProductLabel": "Select Product *", "@selectProductLabel": {"description": "Select product label"}, "pleaseSelectProduct": "Please select a product", "@pleaseSelectProduct": {"description": "Please select product message"}, "quantityAndPriceTitle": "Quantity & Price", "@quantityAndPriceTitle": {"description": "Quantity and price title"}, "quantityLabel": "Quantity *", "@quantityLabel": {"description": "Quantity label"}, "unitLabel": "pcs", "@unitLabel": {"description": "Unit label"}, "pleaseEnterQuantity": "Please enter quantity", "@pleaseEnterQuantity": {"description": "Please enter quantity message"}, "invalidQuantity": "Invalid quantity", "@invalidQuantity": {"description": "Invalid quantity message"}, "unitPriceLabel": "Unit Price", "@unitPriceLabel": {"description": "Unit price label"}, "invalidUnitPrice": "Invalid unit price", "@invalidUnitPrice": {"description": "Invalid unit price message"}, "additionalInfoTitle": "Additional Information", "@additionalInfoTitle": {"description": "Additional information title"}, "referenceNumberLabel": "Reference Number", "@referenceNumberLabel": {"description": "Reference number label"}, "referenceNumberHint": "e.g.: PO001, SO001", "@referenceNumberHint": {"description": "Reference number hint"}, "noteLabel": "Note", "@noteLabel": {"description": "Note label"}, "partnerNameLabel": "Partner Name", "@partnerNameLabel": {"description": "Partner name label"}, "partnerNameHint": "Enter supplier or customer name", "@partnerNameHint": {"description": "Partner name hint"}, "pleaseEnterPartnerName": "Please enter partner name", "@pleaseEnterPartnerName": {"description": "Please enter partner name message"}, "productListTitle": "Product List", "@productListTitle": {"description": "Product list title"}, "addProductButton": "Add Product", "@addProductButton": {"description": "Add product button"}, "noteHint": "Enter note for transaction (optional)", "@noteHint": {"description": "Note hint"}, "stockInRadioLabel": "Stock In", "@stockInRadioLabel": {"description": "Stock in radio label"}, "stockOutRadioLabel": "Stock Out", "@stockOutRadioLabel": {"description": "Stock out radio label"}, "noProductsYetMessage": "No products yet", "@noProductsYetMessage": {"description": "No products yet message"}, "clickAddProductToStart": "Click \"Add Product\" to start", "@clickAddProductToStart": {"description": "Click add product to start instruction"}, "dialogProductNameLabel": "Product Name", "@dialogProductNameLabel": {"description": "Product name label in dialog"}, "dialogPleaseEnterProductName": "Please enter product name", "@dialogPleaseEnterProductName": {"description": "Please enter product name message in dialog"}, "quantityMustBeGreaterThanZero": "Quantity must be greater than 0", "@quantityMustBeGreaterThanZero": {"description": "Quantity must be greater than zero message"}, "pleaseEnterUnitPrice": "Please enter unit price", "@pleaseEnterUnitPrice": {"description": "Please enter unit price message"}, "unitPriceMustBeGreaterThanZero": "Unit price must be greater than 0", "@unitPriceMustBeGreaterThanZero": {"description": "Unit price must be greater than zero message"}, "pleaseAddAtLeastOneProduct": "Please add at least one product", "@pleaseAddAtLeastOneProduct": {"description": "Please add at least one product message"}, "inventoryNoProductsYet": "No products yet", "@inventoryNoProductsYet": {"description": "No products yet message in inventory"}, "inventoryAddFirstProduct": "Add your first product", "@inventoryAddFirstProduct": {"description": "Add first product instruction in inventory"}, "partnerManagement": "Partner Management", "@partnerManagement": {"description": "Partner management title"}, "addPartnerTooltip": "Add Partner", "@addPartnerTooltip": {"description": "Add partner tooltip"}, "refreshTooltip": "Refresh", "@refreshTooltip": {"description": "Refresh tooltip"}, "totalPartners": "Total Partners", "@totalPartners": {"description": "Total partners label"}, "activePartners": "Active", "@activePartners": {"description": "Active partners label"}, "customersLabel": "Customers", "@customersLabel": {"description": "Customers label"}, "suppliersLabel": "Suppliers", "@suppliersLabel": {"description": "Suppliers label"}, "searchPartners": "Search Partners", "@searchPartners": {"description": "Search partners label"}, "searchPartnerHint": "Enter name, code, phone...", "@searchPartnerHint": {"description": "Search partner hint"}, "noPartnersFound": "No partners found", "@noPartnersFound": {"description": "No partners found message"}, "noPartnersYet": "No partners yet", "@noPartnersYet": {"description": "No partners yet message"}, "tryDifferentKeywords": "Try searching with different keywords", "@tryDifferentKeywords": {"description": "Try different keywords suggestion"}, "addFirstPartner": "Add your first partner to get started", "@addFirstPartner": {"description": "Add first partner instruction"}, "addPartnerButton": "Add Partner", "@addPartnerButton": {"description": "Add partner button"}, "posMenuTitle": "Sales (POS)", "@posMenuTitle": {"description": "POS sales menu title"}, "notificationsMenuTitle": "Notifications", "@notificationsMenuTitle": {"description": "Notifications menu title"}, "stockTransactionsMenuTitle": "Stock Transactions", "@stockTransactionsMenuTitle": {"description": "Stock transactions menu title"}, "transactionNotFound": "Transaction not found", "@transactionNotFound": {"description": "Transaction not found message"}, "transactionDetails": "Transaction Details", "@transactionDetails": {"description": "Transaction details title"}, "transactionEditTooltip": "Edit", "@transactionEditTooltip": {"description": "Edit transaction tooltip"}, "transactionSaveChanges": "Save", "@transactionSaveChanges": {"description": "Save transaction changes button"}, "deleteTransactionButton": "Delete Transaction", "@deleteTransactionButton": {"description": "Delete transaction button"}, "confirmDeleteTransaction": "Confirm Delete", "@confirmDeleteTransaction": {"description": "Confirm delete transaction title"}, "deleteTransactionMessage": "Are you sure you want to delete transaction", "@deleteTransactionMessage": {"description": "Delete transaction confirmation message"}, "deleteButton": "Delete", "@deleteButton": {"description": "Delete button"}, "systemErrorOccurred": "An error occurred", "@systemErrorOccurred": {"description": "System error occurred message"}, "tryAgainButton": "Try Again", "@tryAgainButton": {"description": "Try again button"}, "partnerLabel": "Partner", "@partnerLabel": {"description": "Partner label"}, "createdBy": "Created By", "@createdBy": {"description": "Created by label"}, "noteFieldLabel": "Note", "@noteFieldLabel": {"description": "Note field label"}, "enterNoteHint": "Enter note...", "@enterNoteHint": {"description": "Enter note hint"}, "noNoteAvailable": "No note available", "@noNoteAvailable": {"description": "No note available message"}, "productListLabel": "Product List", "@productListLabel": {"description": "Product list label"}, "productCountLabel": "Product Count", "@productCountLabel": {"description": "Product count label"}, "totalQuantityLabel": "Total Quantity", "@totalQuantityLabel": {"description": "Total quantity label"}, "totalValueLabel": "Total Value", "@totalValueLabel": {"description": "Total value label"}, "summaryLabel": "Summary", "@summaryLabel": {"description": "Summary label"}, "cancelEditButton": "Cancel", "@cancelEditButton": {"description": "Cancel edit button"}, "saveChangesButton": "Save Changes", "@saveChangesButton": {"description": "Save changes button"}, "transactionUpdatedSuccess": "Transaction updated successfully", "@transactionUpdatedSuccess": {"description": "Transaction updated success message"}, "errorUpdatingTransaction": "Error updating transaction", "@errorUpdatingTransaction": {"description": "Error updating transaction message"}, "transactionDeletedSuccess": "Transaction deleted successfully", "@transactionDeletedSuccess": {"description": "Transaction deleted success message"}, "errorDeletingTransaction": "Error deleting transaction", "@errorDeletingTransaction": {"description": "Error deleting transaction message"}, "addTransactionTitle": "Add Transaction", "@addTransactionTitle": {"description": "Add transaction title"}, "noTransactionsYet": "No transactions yet", "@noTransactionsYet": {"description": "No transactions yet message"}, "addFirstTransaction": "Add your first transaction to get started", "@addFirstTransaction": {"description": "Add first transaction instruction"}, "noChartData": "No chart data available", "@noChartData": {"description": "No chart data message"}, "addTransactionForChart": "Add transactions to view cash flow chart", "@addTransactionForChart": {"description": "Add transaction for chart instruction"}, "financialOverview": "Financial Overview", "@financialOverview": {"description": "Financial overview title"}, "searchCategoriesHint": "Search categories...", "@searchCategoriesHint": {"description": "Search categories hint"}, "transactionDetailsTitle": "Transaction Details", "@transactionDetailsTitle": {"description": "Transaction details title"}, "descriptionLabel": "Description", "@descriptionLabel": {"description": "Description label"}, "typeLabel": "Type", "@typeLabel": {"description": "Type label"}, "categoryLabel": "Category", "@categoryLabel": {"description": "Category label"}, "amountLabel": "Amount", "@amountLabel": {"description": "Amount label"}, "incomeTypeLabel": "Income", "@incomeTypeLabel": {"description": "Income type"}, "expenseTypeLabel": "Expense", "@expenseTypeLabel": {"description": "Expense type"}, "orderDetailsLabel": "Details", "@orderDetailsLabel": {"description": "Order details label"}, "subtotalLabel": "Subtotal", "@subtotalLabel": {"description": "Subtotal label"}, "createNewButton": "Create New", "@createNewButton": {"description": "Create new button"}, "editButton": "Edit", "@editButton": {"description": "Edit button"}, "viewDetails": "View Details", "@viewDetails": {"description": "View details button"}, "copy": "Copy", "@copy": {"description": "Copy button"}, "share": "Share", "@share": {"description": "Share button"}, "print": "Print", "@print": {"description": "Print button"}, "export": "Export", "@export": {"description": "Export button"}, "import": "Import", "@import": {"description": "Import button"}, "upload": "Upload", "@upload": {"description": "Upload button"}, "download": "Download", "@download": {"description": "Download button"}, "sync": "Sync", "@sync": {"description": "Sync button"}, "refreshButton": "Refresh", "@refreshButton": {"description": "Refresh button"}, "restore": "Rest<PERSON>", "@restore": {"description": "Restore button"}, "backup": "Backup", "@backup": {"description": "Backup button"}, "options": "Options", "@options": {"description": "Options button"}, "filtersButton": "Filters", "@filtersButton": {"description": "Filters button"}, "sortButton": "Sort", "@sortButton": {"description": "Sort button"}, "searchButton": "Search", "@searchButton": {"description": "Search button"}, "results": "Results", "@results": {"description": "Results label"}, "page": "Page", "@page": {"description": "Page label"}, "next": "Next", "@next": {"description": "Next button"}, "previous": "Previous", "@previous": {"description": "Previous button"}, "first": "First", "@first": {"description": "First button"}, "last": "Last", "@last": {"description": "Last button"}, "all": "All", "@all": {"description": "All option"}, "none": "None", "@none": {"description": "None option"}, "yes": "Yes", "@yes": {"description": "Yes option"}, "no": "No", "@no": {"description": "No option"}, "trueValue": "True", "@trueValue": {"description": "True value"}, "falseValue": "False", "@falseValue": {"description": "False value"}, "on": "On", "@on": {"description": "On state"}, "off": "Off", "@off": {"description": "Off state"}, "invoiceTitle": "Invoice", "@invoiceTitle": {"description": "Invoice title"}, "salesInvoice": "Sales Invoice", "@salesInvoice": {"description": "Sales invoice"}, "invoiceNumber": "No.", "@invoiceNumber": {"description": "Invoice number"}, "dateLabel": "Date", "@dateLabel": {"description": "Date label"}, "taxLabel": "Tax", "@taxLabel": {"description": "Tax label"}, "discountLabel": "Discount", "@discountLabel": {"description": "Discount label"}, "totalAmountLabel": "Total", "@totalAmountLabel": {"description": "Total amount label"}, "paymentMethodLabel": "Payment", "@paymentMethodLabel": {"description": "Payment method label"}, "thankYouMessage": "Thank you!", "@thankYouMessage": {"description": "Thank you message"}, "closeDialogButton": "Close", "@closeDialogButton": {"description": "Close dialog button"}, "printInvoiceTitle": "Print Invoice", "@printInvoiceTitle": {"description": "Print invoice title"}, "printInvoiceQuestion": "Do you want to print invoice for this order?", "@printInvoiceQuestion": {"description": "Print invoice question"}, "noButton": "No", "@noButton": {"description": "No button"}, "printInvoiceButton": "Print Invoice", "@printInvoiceButton": {"description": "Print invoice button"}, "confirmationTitle": "Confirmation", "@confirmationTitle": {"description": "Confirmation title"}, "clearCartConfirmation": "Are you sure you want to remove all products from cart?", "@clearCartConfirmation": {"description": "Clear cart confirmation"}, "paymentProcessingError": "Payment processing error", "@paymentProcessingError": {"description": "Payment processing error"}, "invoiceDetailTitle": "Invoice Details", "@invoiceDetailTitle": {"description": "Invoice detail title"}, "invoiceInformation": "Invoice Information", "@invoiceInformation": {"description": "Invoice information"}, "invoiceNumberLabel": "Invoice Number", "@invoiceNumberLabel": {"description": "Invoice number label"}, "createdDateLabel": "Created Date", "@createdDateLabel": {"description": "Created date label"}, "statusLabel": "Status", "@statusLabel": {"description": "Status label"}, "createdByLabel": "Created By", "@createdByLabel": {"description": "Created by label"}, "notesLabel": "Notes", "@notesLabel": {"description": "Notes label"}, "cashPayment": "Cash", "@cashPayment": {"description": "Cash payment"}, "creditCardPayment": "Credit Card", "@creditCardPayment": {"description": "Credit card payment"}, "bankTransferPayment": "Bank Transfer", "@bankTransferPayment": {"description": "Bank transfer payment"}, "eWalletPayment": "E-Wallet", "@eWalletPayment": {"description": "E-wallet payment"}, "otherPayment": "Other", "@otherPayment": {"description": "Other payment"}, "paidStatus": "Paid", "@paidStatus": {"description": "Paid status"}, "unpaidStatus": "Unpaid", "@unpaidStatus": {"description": "Unpaid status"}, "processingStatus": "Processing", "@processingStatus": {"description": "Processing status"}, "cancelledStatus": "Cancelled", "@cancelledStatus": {"description": "Cancelled status"}, "completedStatus": "Completed", "@completedStatus": {"description": "Completed status"}, "pendingConfirmationStatus": "Pending Confirmation", "@pendingConfirmationStatus": {"description": "Pending confirmation status"}, "confirmedStatus": "Confirmed", "@confirmedStatus": {"description": "Confirmed status"}, "shippingStatus": "Shipping", "@shippingStatus": {"description": "Shipping status"}, "deliveredStatus": "Delivered", "@deliveredStatus": {"description": "Delivered status"}, "returnedStatus": "Returned", "@returnedStatus": {"description": "Returned status"}, "refundedStatus": "Refunded", "@refundedStatus": {"description": "Refunded status"}, "errorStatus": "Error", "@errorStatus": {"description": "Error status"}, "successStatus": "Success", "@successStatus": {"description": "Success status"}, "warningStatus": "Warning", "@warningStatus": {"description": "Warning status"}, "infoStatus": "Info", "@infoStatus": {"description": "Info status"}, "confirmStatus": "Confirm", "@confirmStatus": {"description": "Confirm status"}, "rejectStatus": "Reject", "@rejectStatus": {"description": "Reject status"}, "showLabel": "Show", "@showLabel": {"description": "Show label"}, "hideLabel": "<PERSON>de", "@hideLabel": {"description": "Hide label"}, "openLabel": "Open", "@openLabel": {"description": "Open label"}, "startLabel": "Start", "@startLabel": {"description": "Start label"}, "endLabel": "End", "@endLabel": {"description": "End label"}, "pauseLabel": "Pause", "@pauseLabel": {"description": "Pause label"}, "continueLabel": "Continue", "@continueLabel": {"description": "Continue label"}, "stopLabel": "Stop", "@stopLabel": {"description": "Stop label"}, "restartLabel": "<PERSON><PERSON>", "@restartLabel": {"description": "Restart label"}, "updateLabel": "Update", "@updateLabel": {"description": "Update label"}, "reloadLabel": "Reload", "@reloadLabel": {"description": "Reload label"}, "pasteLabel": "Paste", "@pasteLabel": {"description": "Paste label"}, "cutLabel": "Cut", "@cutLabel": {"description": "Cut label"}, "undoLabel": "Undo", "@undoLabel": {"description": "Undo label"}, "redoLabel": "Redo", "@redoLabel": {"description": "Redo label"}, "groupLabel": "Group", "@groupLabel": {"description": "Group label"}, "classifyLabel": "Classify", "@classifyLabel": {"description": "Classify label"}, "tagLabel": "Tag", "@tagLabel": {"description": "Tag label"}, "labelLabel": "Label", "@labelLabel": {"description": "Label label"}, "colorLabel": "Color", "@colorLabel": {"description": "Color label"}, "sizeLabel": "Size", "@sizeLabel": {"description": "Size label"}, "weightLabel": "Weight", "@weightLabel": {"description": "Weight label"}, "heightLabel": "Height", "@heightLabel": {"description": "Height label"}, "widthLabel": "<PERSON><PERSON><PERSON>", "@widthLabel": {"description": "Width label"}, "lengthLabel": "Length", "@lengthLabel": {"description": "Length label"}, "areaLabel": "Area", "@areaLabel": {"description": "Area label"}, "volumeLabel": "Volume", "@volumeLabel": {"description": "Volume label"}, "quantityFieldLabel": "Quantity", "@quantityFieldLabel": {"description": "Quantity field label"}, "unitPriceFieldLabel": "Unit Price", "@unitPriceFieldLabel": {"description": "Unit price field label"}, "totalPriceLabel": "Total Price", "@totalPriceLabel": {"description": "Total price label"}, "grandTotalLabel": "Grand Total", "@grandTotalLabel": {"description": "Grand total label"}, "changeLabel": "Change", "@changeLabel": {"description": "Change label"}, "receivedLabel": "Received", "@receivedLabel": {"description": "Received label"}, "feeLabel": "Fee", "@feeLabel": {"description": "Fee label"}, "promotionLabel": "Promotion", "@promotionLabel": {"description": "Promotion label"}, "voucherLabel": "Voucher", "@voucherLabel": {"description": "Voucher label"}, "discountCodeLabel": "Discount Code", "@discountCodeLabel": {"description": "Discount code label"}, "loyaltyPointsLabel": "Loyalty Points", "@loyaltyPointsLabel": {"description": "Loyalty points label"}, "accumulateLabel": "Accumulate", "@accumulateLabel": {"description": "Accumulate label"}, "exchangeLabel": "Exchange", "@exchangeLabel": {"description": "Exchange label"}, "convertLabel": "Convert", "@convertLabel": {"description": "Convert label"}, "exchangeRateLabel": "Exchange Rate", "@exchangeRateLabel": {"description": "Exchange rate label"}, "unitFieldLabel": "Unit", "@unitFieldLabel": {"description": "Unit field label"}, "packageLabel": "Package", "@packageLabel": {"description": "Package label"}, "boxLabel": "Box", "@boxLabel": {"description": "Box label"}, "cartonLabel": "<PERSON><PERSON>", "@cartonLabel": {"description": "Carton label"}, "bagLabel": "Bag", "@bagLabel": {"description": "Bag label"}, "bottleLabel": "<PERSON><PERSON>", "@bottleLabel": {"description": "Bottle label"}, "jarLabel": "<PERSON><PERSON>", "@jarLabel": {"description": "Jar label"}, "sackLabel": "Sack", "@sackLabel": {"description": "Sack label"}, "pieceLabel": "Piece", "@pieceLabel": {"description": "Piece label"}, "itemLabel": "<PERSON><PERSON>", "@itemLabel": {"description": "Item label"}, "setLabel": "Set", "@setLabel": {"description": "Set label"}, "pairLabel": "Pair", "@pairLabel": {"description": "Pair label"}, "coupleLabel": "<PERSON><PERSON><PERSON>", "@coupleLabel": {"description": "Couple label"}, "kilogramLabel": "Kg", "@kilogramLabel": {"description": "Kilogram label"}, "gramLabel": "Gram", "@gramLabel": {"description": "Gram label"}, "literLabel": "Liter", "@literLabel": {"description": "Liter label"}, "milliliterLabel": "Ml", "@milliliterLabel": {"description": "Milliliter label"}, "meterLabel": "<PERSON>er", "@meterLabel": {"description": "Meter label"}, "centimeterLabel": "Cm", "@centimeterLabel": {"description": "Centimeter label"}, "millimeterLabel": "Mm", "@millimeterLabel": {"description": "Millimeter label"}, "inchLabel": "Inch", "@inchLabel": {"description": "Inch label"}, "feetLabel": "Feet", "@feetLabel": {"description": "Feet label"}, "yardLabel": "Yard", "@yardLabel": {"description": "Yard label"}, "customerNameNote": "Customer", "@customerNameNote": {"description": "Customer name note"}, "customerPhoneNote": "Phone", "@customerPhoneNote": {"description": "Customer phone note"}, "salesScreenTitle": "Sales", "@salesScreenTitle": {"description": "Sales screen title"}, "newSaleButton": "New Sale", "@newSaleButton": {"description": "New sale button"}, "salesAnalytics": "Sales Analytics", "@salesAnalytics": {"description": "Sales analytics"}, "ordersListTitle": "Orders List", "@ordersListTitle": {"description": "Orders list title"}, "orderDetailsTitle": "Order Details", "@orderDetailsTitle": {"description": "Order details title"}, "orderNumber": "Order Number", "@orderNumber": {"description": "Order number"}, "orderStatusField": "Order Status", "@orderStatusField": {"description": "Order status field"}, "orderDate": "Order Date", "@orderDate": {"description": "Order date"}, "orderTime": "Order Time", "@orderTime": {"description": "Order time"}, "customerInfo": "Customer Information", "@customerInfo": {"description": "Customer information"}, "customerNameField": "Customer Name", "@customerNameField": {"description": "Customer name field"}, "customerPhoneField": "Phone Number", "@customerPhoneField": {"description": "Customer phone field"}, "customerAddressField": "Address", "@customerAddressField": {"description": "Customer address field"}, "orderNotesField": "Order Notes", "@orderNotesField": {"description": "Order notes field"}, "paymentMethodField": "Payment Method", "@paymentMethodField": {"description": "Payment method field"}, "amountPaidField": "Amount <PERSON>", "@amountPaidField": {"description": "Amount paid field"}, "changeAmountField": "Change", "@changeAmountField": {"description": "Change amount field"}, "vatAmountField": "VAT", "@vatAmountField": {"description": "VAT amount field"}, "discountAmountField": "Discount Amount", "@discountAmountField": {"description": "Discount amount field"}, "promotionCodeField": "Promotion Code", "@promotionCodeField": {"description": "Promotion code field"}, "loyaltyPointsField": "Loyalty Points", "@loyaltyPointsField": {"description": "Loyalty points field"}, "membershipField": "Membership", "@membershipField": {"description": "Membership field"}, "guestCustomer": "Guest Customer", "@guestCustomer": {"description": "Guest customer"}, "orderStatusPaid": "Paid", "@orderStatusPaid": {"description": "Paid order status"}, "orderStatusUnpaid": "Unpaid", "@orderStatusUnpaid": {"description": "Unpaid order status"}, "orderStatusProcessing": "Processing", "@orderStatusProcessing": {"description": "Processing order status"}, "orderStatusCompleted": "Completed", "@orderStatusCompleted": {"description": "Completed order status"}, "orderStatusCancelled": "Cancelled", "@orderStatusCancelled": {"description": "Cancelled order status"}, "orderStatusReturned": "Returned", "@orderStatusReturned": {"description": "Returned order status"}, "orderStatusRefunded": "Refunded", "@orderStatusRefunded": {"description": "Refunded order status"}, "printInvoiceAction": "Print Invoice", "@printInvoiceAction": {"description": "Print invoice action"}, "sendInvoiceAction": "Send Invoice", "@sendInvoiceAction": {"description": "Send invoice action"}, "saveOrderAction": "Save Order", "@saveOrderAction": {"description": "Save order action"}, "cancelOrderAction": "Cancel Order", "@cancelOrderAction": {"description": "Cancel order action"}, "confirmPaymentAction": "Confirm Payment", "@confirmPaymentAction": {"description": "Confirm payment action"}, "undoAction": "Undo", "@undoAction": {"description": "Undo action"}, "refreshAction": "Refresh", "@refreshAction": {"description": "Refresh action"}, "reloadAction": "Reload", "@reloadAction": {"description": "Reload action"}, "syncAction": "Sync", "@syncAction": {"description": "Sync action"}, "backupAction": "Backup", "@backupAction": {"description": "Backup action"}, "restoreAction": "Rest<PERSON>", "@restoreAction": {"description": "Restore action"}, "searchProductsHint": "Search products", "@searchProductsHint": {"description": "Search products hint"}, "scanBarcodeAction": "Scan Barcode", "@scanBarcodeAction": {"description": "Scan barcode action"}, "addToCartAction": "Add to Cart", "@addToCartAction": {"description": "Add to cart action"}, "removeFromCartAction": "Remove from Cart", "@removeFromCartAction": {"description": "Remove from cart action"}, "updateQuantityAction": "Update Quantity", "@updateQuantityAction": {"description": "Update quantity action"}, "clearAllCartAction": "Clear All", "@clearAllCartAction": {"description": "Clear all cart action"}, "continueShoppingAction": "Continue Shopping", "@continueShoppingAction": {"description": "Continue shopping action"}, "checkoutNowAction": "Checkout Now", "@checkoutNowAction": {"description": "Checkout now action"}, "selectCustomerAction": "Select Customer", "@selectCustomerAction": {"description": "Select customer action"}, "addNewCustomerAction": "Add New Customer", "@addNewCustomerAction": {"description": "Add new customer action"}, "applyDiscountAction": "Apply Discount", "@applyDiscountAction": {"description": "Apply discount action"}, "calculateTaxAction": "Calculate Tax", "@calculateTaxAction": {"description": "Calculate tax action"}, "previewInvoiceAction": "Preview Invoice", "@previewInvoiceAction": {"description": "Preview invoice action"}, "salesReportsTitle": "Sales Reports", "@salesReportsTitle": {"description": "Sales reports title"}, "salesStatisticsTitle": "Sales Statistics", "@salesStatisticsTitle": {"description": "Sales statistics title"}, "salesHistoryTitle": "Sales History", "@salesHistoryTitle": {"description": "Sales history title"}, "transactionHistoryTitle": "Transaction History", "@transactionHistoryTitle": {"description": "Transaction history title"}, "revenueLabel": "Revenue", "@revenueLabel": {"description": "Revenue label"}, "profitLabel": "Profit", "@profitLabel": {"description": "Profit label"}, "quantitySoldLabel": "Quantity Sold", "@quantitySoldLabel": {"description": "Quantity sold label"}, "bestSellingProductsLabel": "Best Selling Products", "@bestSellingProductsLabel": {"description": "Best selling products label"}, "loyalCustomersLabel": "Loyal Customers", "@loyalCustomersLabel": {"description": "Loyal customers label"}, "productCatalogTitle": "Product Catalog", "@productCatalogTitle": {"description": "Product catalog title"}, "productSelectionTitle": "Product Selection", "@productSelectionTitle": {"description": "Product selection title"}, "cartManagementTitle": "Cart Management", "@cartManagementTitle": {"description": "Cart management title"}, "checkoutScreenTitle": "Checkout", "@checkoutScreenTitle": {"description": "Checkout screen title"}, "receiptPrintingTitle": "Receipt Printing", "@receiptPrintingTitle": {"description": "Receipt printing title"}, "scanBarcodeTitle": "Scan Barcode", "@scanBarcodeTitle": {"description": "Scan barcode dialog title"}, "enterBarcodeLabel": "Enter Barcode", "@enterBarcodeLabel": {"description": "Enter barcode label"}, "scanOrEnterBarcodeHint": "Scan or enter product code...", "@scanOrEnterBarcodeHint": {"description": "Scan or enter barcode hint"}, "orUseCameraToScan": "Or use camera to scan barcode", "@orUseCameraToScan": {"description": "Camera scan instruction"}, "addedToCartMessage": "Added {productName} to cart", "@addedToCartMessage": {"description": "Product added to cart message", "placeholders": {"productName": {"type": "String"}}}, "productNotFoundMessage": "Product not found with code: {barcode}", "@productNotFoundMessage": {"description": "Product not found message", "placeholders": {"barcode": {"type": "String"}}}, "cashMethodLabel": "Cash", "@cashMethodLabel": {"description": "Cash payment method label"}, "creditCardMethodLabel": "Credit Card", "@creditCardMethodLabel": {"description": "Credit card payment method label"}, "bankTransferMethodLabel": "Bank Transfer", "@bankTransferMethodLabel": {"description": "Bank transfer payment method label"}, "eWalletMethodLabel": "E-Wallet", "@eWalletMethodLabel": {"description": "E-wallet payment method label"}, "otherMethodLabel": "Other", "@otherMethodLabel": {"description": "Other payment method label"}, "amountReceivedLabel": "Amount Received", "@amountReceivedLabel": {"description": "Amount received label"}, "changeAmountLabel": "Change", "@changeAmountLabel": {"description": "Change amount label"}, "enterAmountReceivedHint": "Enter amount received from customer", "@enterAmountReceivedHint": {"description": "Enter amount received hint"}, "exactAmountButton": "Exact Amount", "@exactAmountButton": {"description": "Exact amount button"}, "processPaymentButton": "Process Payment", "@processPaymentButton": {"description": "Process payment button"}, "processingPaymentLabel": "Processing...", "@processingPaymentLabel": {"description": "Processing payment label"}, "returningToDashboard": "Returning to Dashboard", "@returningToDashboard": {"description": "Notification when returning to dashboard"}, "appResumed": "App resumed", "@appResumed": {"description": "Notification when app is resumed from background"}, "dataUpdated": "📊 Data has been updated", "@dataUpdated": {"description": "Notification when data is refreshed"}, "manualRefresh": "Manual refresh", "@manualRefresh": {"description": "Manual refresh action"}, "refreshData": "Refresh", "@refreshData": {"description": "Tooltip for refreshing data"}, "errorLoadingData": "Error loading data: {error}", "@errorLoadingData": {"description": "Error message when data fails to load", "placeholders": {"error": {"type": "String"}}}, "retryAfterError": "Retry after error", "@retryAfterError": {"description": "Retry action after error"}, "barcodeDialogTitle": "Scan Barcode", "@barcodeDialogTitle": {"description": "Title for barcode scanning dialog"}, "barcodeDialogMessage": "Scan a barcode to add item to cart", "@barcodeDialogMessage": {"description": "Message in barcode scanning dialog"}, "barcodeDialogScanButton": "<PERSON><PERSON>", "@barcodeDialogScanButton": {"description": "Scan button label in barcode dialog"}, "barcodeDialogCancelLabel": "Cancel", "@barcodeDialogCancelLabel": {"description": "Cancel button label in barcode dialog"}, "printReceiptDialogTitle": "Print Receipt", "@printReceiptDialogTitle": {"description": "Print receipt dialog title"}, "printReceiptDialogContent": "Do you want to print receipt for this order?", "@printReceiptDialogContent": {"description": "Print receipt dialog content"}, "noButtonLabel": "No", "@noButtonLabel": {"description": "No button label"}, "printReceiptButtonLabel": "Print Receipt", "@printReceiptButtonLabel": {"description": "Print receipt button label"}, "taxReceiptLabel": "Tax", "@taxReceiptLabel": {"description": "Tax label in receipt"}, "discountReceiptLabel": "Discount", "@discountReceiptLabel": {"description": "Discount label in receipt"}, "lineItemTotalLabel": "Line Total", "@lineItemTotalLabel": {"description": "Line total label for each item"}, "checkoutButtonLabel": "Checkout", "@checkoutButtonLabel": {"description": "Checkout button label"}, "saveDraftButtonLabel": "Save Draft", "@saveDraftButtonLabel": {"description": "Save draft button label"}, "customerInfoTooltip": "Customer Information", "@customerInfoTooltip": {"description": "Customer information tooltip"}, "discountTooltip": "Discount", "@discountTooltip": {"description": "Discount tooltip"}, "customerInfoDialogTitle": "Customer Information", "@customerInfoDialogTitle": {"description": "Customer information dialog title"}, "customerNameFieldLabel": "Customer Name", "@customerNameFieldLabel": {"description": "Customer name field label"}, "customerPhoneFieldLabel": "Phone Number", "@customerPhoneFieldLabel": {"description": "Customer phone field label"}, "saveButtonLabel": "Save", "@saveButtonLabel": {"description": "Save button label"}, "discountDialogTitle": "Discount", "@discountDialogTitle": {"description": "Discount dialog title"}, "discountAmountFieldLabel": "Discount Amount", "@discountAmountFieldLabel": {"description": "Discount amount field label"}, "subtotalDisplayLabel": "Subtotal", "@subtotalDisplayLabel": {"description": "Subtotal display label"}, "applyButtonLabel": "Apply", "@applyButtonLabel": {"description": "Apply button label"}, "checkoutDialogTitle": "Checkout", "@checkoutDialogTitle": {"description": "Checkout dialog title"}, "subtotalCheckoutLabel": "Subtotal", "@subtotalCheckoutLabel": {"description": "Subtotal label in checkout"}, "taxCheckoutLabel": "Tax (10%)", "@taxCheckoutLabel": {"description": "Tax label in checkout"}, "discountCheckoutLabel": "Discount", "@discountCheckoutLabel": {"description": "Discount label in checkout"}, "totalCheckoutLabel": "Total", "@totalCheckoutLabel": {"description": "Total label in checkout"}, "paymentMethodCheckoutLabel": "Payment Method", "@paymentMethodCheckoutLabel": {"description": "Payment method label in checkout"}, "customerCashLabel": "Cash Received", "@customerCashLabel": {"description": "Cash received label"}, "changeCheckoutLabel": "Change", "@changeCheckoutLabel": {"description": "Change label in checkout"}, "completeButtonLabel": "Complete", "@completeButtonLabel": {"description": "Complete button label"}, "insufficientCashMessage": "Insufficient cash received for payment", "@insufficientCashMessage": {"description": "Insufficient cash message"}, "orderCreatedSuccessMessage": "Order created successfully", "@orderCreatedSuccessMessage": {"description": "Order created success message"}, "itemCountLabel": "items", "@itemCountLabel": {"description": "Item count label"}, "revenue": "Revenue", "@revenue": {"description": "Revenue label in reports"}, "profit": "Profit", "@profit": {"description": "Profit label in reports"}, "orderCount": "Order Count", "@orderCount": {"description": "Order count"}, "inventoryValue": "Inventory", "@inventoryValue": {"description": "Inventory value label"}, "dateRangeLabel": "Date Range:", "@dateRangeLabel": {"description": "Date range label"}, "overview": "Overview", "@overview": {"description": "Overview title"}, "salesReportTitle": "Sales Report", "@salesReportTitle": {"description": "Sales report title"}, "totalCost": "Total Cost", "@totalCost": {"description": "Total cost"}, "grossProfit": "Gross Profit", "@grossProfit": {"description": "Gross profit"}, "profitMargin": "<PERSON><PERSON>", "@profitMargin": {"description": "Profit margin"}, "inventoryReportTitle": "Inventory Report", "@inventoryReportTitle": {"description": "Inventory report title"}, "inventoryWorth": "Inventory Value", "@inventoryWorth": {"description": "Inventory worth"}, "lowStockItems": "Low Stock", "@lowStockItems": {"description": "Low stock items"}, "outOfStockItems": "Out of Stock", "@outOfStockItems": {"description": "Out of stock items"}, "financeReportTitle": "Finance Report", "@financeReportTitle": {"description": "Finance report title"}, "totalIncome": "Total Income", "@totalIncome": {"description": "Total income"}, "totalExpense": "Total Expense", "@totalExpense": {"description": "Total expense"}, "netCashFlow": "Net Cash Flow", "@netCashFlow": {"description": "Net cash flow"}, "endingBalance": "Ending Balance", "@endingBalance": {"description": "Ending balance"}, "topSellingProducts": "Top Selling Products", "@topSellingProducts": {"description": "Top selling products"}, "sold": "Sold", "@sold": {"description": "Sold label"}, "inventoryAlerts": "Inventory Alerts", "@inventoryAlerts": {"description": "Inventory alerts"}, "allProductsInStock": "All products have sufficient stock", "@allProductsInStock": {"description": "Message when all products have sufficient stock"}, "stockInfo": "Stock: {current} / Min: {min}", "@stockInfo": {"description": "Stock information", "placeholders": {"current": {}, "min": {}}}}