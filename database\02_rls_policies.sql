-- =====================================================
-- CITY POS - ROW LEVEL SECURITY POLICIES
-- =====================================================
-- This file contains all RLS policies for City POS
-- Run this script after creating tables

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.partners ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stock_transactions ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- USERS POLICIES
-- =====================================================

-- Users can view own profile
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

-- Users can update own profile
CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Admins can view all users
CREATE POLICY "Admins can view all users" ON public.users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Admins can manage all users
CREATE POLICY "Admins can manage all users" ON public.users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- =====================================================
-- CATEGORIES POLICIES
-- =====================================================

-- All authenticated users can view categories
CREATE POLICY "All authenticated users can view categories" ON public.categories
  FOR SELECT USING (auth.role() = 'authenticated');

-- Managers and admins can manage categories
CREATE POLICY "Managers and admins can manage categories" ON public.categories
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

-- =====================================================
-- PRODUCTS POLICIES
-- =====================================================

-- All authenticated users can view products
CREATE POLICY "All authenticated users can view products" ON public.products
  FOR SELECT USING (auth.role() = 'authenticated');

-- Managers and admins can manage products
CREATE POLICY "Managers and admins can manage products" ON public.products
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

-- =====================================================
-- PARTNERS POLICIES
-- =====================================================

-- All authenticated users can view partners
CREATE POLICY "All authenticated users can view partners" ON public.partners
  FOR SELECT USING (auth.role() = 'authenticated');

-- Managers and admins can manage partners
CREATE POLICY "Managers and admins can manage partners" ON public.partners
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

-- =====================================================
-- NOTIFICATIONS POLICIES
-- =====================================================

-- Users can view own notifications
CREATE POLICY "Users can view own notifications" ON public.notifications
  FOR SELECT USING (user_id = auth.uid());

-- Users can update own notifications (mark as read)
CREATE POLICY "Users can update own notifications" ON public.notifications
  FOR UPDATE USING (user_id = auth.uid());

-- System can create notifications for users
CREATE POLICY "System can create notifications" ON public.notifications
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- =====================================================
-- ORDERS POLICIES
-- =====================================================

-- All authenticated users can view orders
CREATE POLICY "All authenticated users can view orders" ON public.orders
  FOR SELECT USING (auth.role() = 'authenticated');

-- All authenticated users can create orders
CREATE POLICY "All authenticated users can create orders" ON public.orders
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Users can update own orders
CREATE POLICY "Users can update own orders" ON public.orders
  FOR UPDATE USING (created_by = auth.uid());

-- Managers and admins can manage all orders
CREATE POLICY "Managers and admins can manage all orders" ON public.orders
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

-- Order items policies
CREATE POLICY "All authenticated users can view order items" ON order_items
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can manage order items" ON order_items
  FOR ALL USING (auth.role() = 'authenticated');

-- Invoices policies
CREATE POLICY "All authenticated users can view invoices" ON invoices
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can create invoices" ON invoices
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update own invoices" ON invoices
  FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "Managers and admins can manage all invoices" ON invoices
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

-- Payments policies
CREATE POLICY "All authenticated users can view payments" ON payments
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can create payments" ON payments
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update own payments" ON payments
  FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "Managers and admins can manage all payments" ON payments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

-- Stock transactions policies
CREATE POLICY "All authenticated users can view stock transactions" ON stock_transactions
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "All authenticated users can create stock transactions" ON stock_transactions
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Managers and admins can manage stock transactions" ON stock_transactions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );
