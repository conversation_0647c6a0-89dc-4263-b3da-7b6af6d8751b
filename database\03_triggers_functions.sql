-- =====================================================
-- CITY POS - TRIGGERS AND FUNCTIONS
-- =====================================================
-- This file contains all triggers and functions for City POS
-- Run this script after creating tables and RLS policies

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- UPDATED_AT TRIGGERS
-- =====================================================

-- Apply updated_at trigger to all tables with updated_at column
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON public.categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON public.products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_partners_updated_at BEFORE UPDATE ON public.partners
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON public.orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at BEFORE UPDATE ON public.invoices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON public.payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- AUTO-NUMBERING FUNCTIONS
-- =====================================================

-- Function to generate order number
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' ||
                           LPAD(NEXTVAL('order_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for order numbers
CREATE SEQUENCE IF NOT EXISTS order_number_seq START 1;

-- Apply order number trigger
CREATE TRIGGER generate_order_number_trigger BEFORE INSERT ON public.orders
    FOR EACH ROW EXECUTE FUNCTION generate_order_number();

-- Function to generate invoice number
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.invoice_number IS NULL OR NEW.invoice_number = '' THEN
        NEW.invoice_number := 'INV-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' ||
                             LPAD(NEXTVAL('invoice_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for invoice numbers
CREATE SEQUENCE IF NOT EXISTS invoice_number_seq START 1;

-- Apply invoice number trigger
CREATE TRIGGER generate_invoice_number_trigger BEFORE INSERT ON public.invoices
    FOR EACH ROW EXECUTE FUNCTION generate_invoice_number();

-- Function to generate payment number
CREATE OR REPLACE FUNCTION generate_payment_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.payment_number IS NULL OR NEW.payment_number = '' THEN
        NEW.payment_number := 'PAY-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' ||
                             LPAD(NEXTVAL('payment_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for payment numbers
CREATE SEQUENCE IF NOT EXISTS payment_number_seq START 1;

-- Apply payment number trigger
CREATE TRIGGER generate_payment_number_trigger BEFORE INSERT ON public.payments
    FOR EACH ROW EXECUTE FUNCTION generate_payment_number();

-- =====================================================
-- BUSINESS LOGIC FUNCTIONS
-- =====================================================

-- Function to create notification
CREATE OR REPLACE FUNCTION create_notification(
    p_user_id UUID,
    p_title TEXT,
    p_message TEXT,
    p_type notification_type DEFAULT 'info',
    p_data JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (user_id, title, message, type, data)
    VALUES (p_user_id, p_title, p_message, p_type, p_data)
    RETURNING id INTO notification_id;

    RETURN notification_id;
END;
$$ language 'plpgsql';

-- Function to update product stock
CREATE OR REPLACE FUNCTION update_product_stock()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Update stock based on transaction type
        IF NEW.type = 'in' THEN
            UPDATE public.products
            SET stock_quantity = stock_quantity + NEW.quantity
            WHERE id = NEW.product_id;
        ELSIF NEW.type = 'out' THEN
            UPDATE public.products
            SET stock_quantity = stock_quantity - NEW.quantity
            WHERE id = NEW.product_id;
        ELSIF NEW.type = 'adjustment' THEN
            UPDATE public.products
            SET stock_quantity = NEW.quantity
            WHERE id = NEW.product_id;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Apply stock update trigger
CREATE TRIGGER update_product_stock_trigger AFTER INSERT ON public.stock_transactions
    FOR EACH ROW EXECUTE FUNCTION update_product_stock();

-- Function to create stock transaction from order items
CREATE OR REPLACE FUNCTION create_stock_transaction_from_order()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create stock transaction for sales orders
    IF (SELECT type FROM public.orders WHERE id = NEW.order_id) = 'sale' THEN
        INSERT INTO public.stock_transactions (
            product_id,
            type,
            quantity,
            unit_cost,
            reference_type,
            reference_id,
            created_by
        ) VALUES (
            NEW.product_id,
            'out',
            NEW.quantity,
            NEW.unit_price,
            'order',
            NEW.order_id,
            (SELECT created_by FROM public.orders WHERE id = NEW.order_id)
        );
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply stock transaction trigger for order items
CREATE TRIGGER create_stock_transaction_trigger AFTER INSERT ON public.order_items
    FOR EACH ROW EXECUTE FUNCTION create_stock_transaction_from_order();

-- Function to create notification when order is completed
CREATE OR REPLACE FUNCTION notify_order_completed()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        PERFORM create_notification(
            NEW.created_by,
            'Đơn hàng hoàn thành',
            'Đơn hàng ' || NEW.order_number || ' đã được hoàn thành thành công.',
            'success',
            jsonb_build_object('order_id', NEW.id, 'order_number', NEW.order_number)
        );
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply order completion notification trigger
CREATE TRIGGER notify_order_completed_trigger AFTER UPDATE ON public.orders
    FOR EACH ROW EXECUTE FUNCTION notify_order_completed();

-- Function to create notification when invoice is created
CREATE OR REPLACE FUNCTION notify_invoice_created()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM create_notification(
        NEW.created_by,
        'Hóa đơn mới',
        'Hóa đơn ' || NEW.invoice_number || ' đã được tạo thành công.',
        'info',
        jsonb_build_object('invoice_id', NEW.id, 'invoice_number', NEW.invoice_number)
    );
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply invoice creation notification trigger
CREATE TRIGGER notify_invoice_created_trigger AFTER INSERT ON public.invoices
    FOR EACH ROW EXECUTE FUNCTION notify_invoice_created();
