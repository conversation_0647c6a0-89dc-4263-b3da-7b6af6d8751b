-- =====================================================
-- CITY POS - SAMPLE DATA
-- =====================================================
-- This file contains sample data for City POS
-- Run this after creating tables and setting up authentication
-- Note: You need to have at least one user in auth.users table first

-- =====================================================
-- SAMPLE CATEGORIES
-- =====================================================

-- Insert sample categories
INSERT INTO public.categories (name, description, color, icon) VALUES
('<PERSON><PERSON> uống', '<PERSON><PERSON><PERSON> loạ<PERSON> nước uố<PERSON>, cà phê, trà', '#10B981', 'local_drink'),
('Thực phẩm', 'Bánh kẹo, snack, thực phẩm khô', '#F59E0B', 'fastfood'),
('Văn phòng phẩm', 'Bút, giấy, dụng cụ văn phòng', '#3B82F6', 'edit'),
('Điện tử', 'Phụ kiện điện tử, sạc, tai nghe', '#8B5CF6', 'devices'),
('Gia dụng', 'Đồ dùng gia đình, nhà bếp', '#EF4444', 'home');

-- =====================================================
-- SAMPLE PRODUCTS
-- =====================================================

-- Insert sample products
INSERT INTO public.products (name, description, sku, barcode, category_id, price, cost, stock_quantity, min_stock_level, unit) VALUES
('Cà phê đen', 'Cà phê đen truyền thống', 'CF001', '1234567890123',
 (SELECT id FROM public.categories WHERE name = 'Đồ uống' LIMIT 1), 25000, 15000, 50, 10, 'ly'),
('Cà phê sữa', 'Cà phê sữa đá', 'CF002', '1234567890124',
 (SELECT id FROM public.categories WHERE name = 'Đồ uống' LIMIT 1), 30000, 18000, 45, 10, 'ly'),
('Trà đá', 'Trà đá chanh', 'TR001', '1234567890125',
 (SELECT id FROM public.categories WHERE name = 'Đồ uống' LIMIT 1), 15000, 8000, 30, 5, 'ly'),
('Bánh mì', 'Bánh mì thịt nướng', 'BM001', '1234567890126',
 (SELECT id FROM public.categories WHERE name = 'Thực phẩm' LIMIT 1), 20000, 12000, 25, 5, 'cái'),
('Kẹo cao su', 'Kẹo cao su không đường', 'KC001', '1234567890127',
 (SELECT id FROM public.categories WHERE name = 'Thực phẩm' LIMIT 1), 5000, 3000, 100, 20, 'gói'),
('Bút bi', 'Bút bi xanh', 'BB001', '1234567890128',
 (SELECT id FROM public.categories WHERE name = 'Văn phòng phẩm' LIMIT 1), 8000, 5000, 80, 15, 'cái'),
('Sạc điện thoại', 'Sạc nhanh USB-C', 'SC001', '1234567890129',
 (SELECT id FROM public.categories WHERE name = 'Điện tử' LIMIT 1), 150000, 100000, 20, 5, 'cái'),
('Ly nhựa', 'Ly nhựa trong suốt', 'LY001', '1234567890130',
 (SELECT id FROM public.categories WHERE name = 'Gia dụng' LIMIT 1), 12000, 8000, 60, 10, 'cái');

-- =====================================================
-- SAMPLE PARTNERS
-- =====================================================

-- Insert sample partners (customers and suppliers)
INSERT INTO public.partners (name, type, email, phone, address) VALUES
('Nguyễn Văn A', 'customer', '<EMAIL>', '0901234567', '123 Đường ABC, Quận 1, TP.HCM'),
('Trần Thị B', 'customer', '<EMAIL>', '0901234568', '456 Đường DEF, Quận 2, TP.HCM'),
('Lê Văn C', 'customer', '<EMAIL>', '0901234569', '789 Đường GHI, Quận 3, TP.HCM'),
('Công ty XYZ', 'supplier', '<EMAIL>', '0281234567', '100 Đường Công nghiệp, Quận 9, TP.HCM'),
('Nhà phân phối ABC', 'supplier', '<EMAIL>', '0281234568', '200 Đường Thương mại, Quận 7, TP.HCM');

-- Insert sample orders (after you have users)
-- You'll need to replace the user_id with actual user ID from auth.users
/*
INSERT INTO orders (order_number, type, status, partner_id, subtotal, tax_amount, total_amount, payment_status, payment_method, created_by) VALUES
('ORD-20241127-0001', 'sale', 'completed', 
 (SELECT id FROM partners WHERE name = 'Nguyễn Văn A' LIMIT 1), 
 55000, 5500, 60500, 'paid', 'cash', 
 (SELECT id FROM auth.users LIMIT 1));

-- Insert sample order items
INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_amount) VALUES
((SELECT id FROM orders WHERE order_number = 'ORD-20241127-0001' LIMIT 1),
 (SELECT id FROM products WHERE name = 'Cà phê đen' LIMIT 1), 2, 25000, 50000),
((SELECT id FROM orders WHERE order_number = 'ORD-20241127-0001' LIMIT 1),
 (SELECT id FROM products WHERE name = 'Kẹo cao su' LIMIT 1), 1, 5000, 5000);

-- Insert sample payments
INSERT INTO payments (payment_number, type, category, amount, payment_method, reference_type, reference_id, description, created_by) VALUES
('PAY-20241127-0001', 'income', 'sales', 60500, 'cash', 'order',
 (SELECT id FROM orders WHERE order_number = 'ORD-20241127-0001' LIMIT 1),
 'Thanh toán đơn hàng ORD-20241127-0001',
 (SELECT id FROM auth.users LIMIT 1));
*/
