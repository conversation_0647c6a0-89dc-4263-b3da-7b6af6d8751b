-- =====================================================
-- CITY POS - SUPABASE STORAGE SETUP
-- =====================================================
-- This file sets up Supabase Storage buckets and policies
-- Run this script in Supabase SQL Editor after creating storage_files table

-- =====================================================
-- CREATE STORAGE BUCKETS
-- =====================================================

-- Create main bucket for City POS files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'city-pos',
  'city-pos', 
  true,
  52428800, -- 50MB limit
  ARRAY[
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/webp',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]
) ON CONFLICT (id) DO NOTHING;

-- Create private bucket for sensitive documents
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'city-pos-private',
  'city-pos-private',
  false,
  104857600, -- 100MB limit
  ARRAY[
    'application/pdf',
    'image/jpeg',
    'image/png',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ]
) ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- STORAGE POLICIES FOR PUBLIC BUCKET (city-pos)
-- =====================================================

-- Policy: Anyone can view public files
CREATE POLICY "Public files are viewable by everyone" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'city-pos');

-- Policy: Authenticated users can upload files
CREATE POLICY "Authenticated users can upload files" 
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'city-pos' 
  AND auth.role() = 'authenticated'
);

-- Policy: Users can update their own files
CREATE POLICY "Users can update own files" 
ON storage.objects FOR UPDATE 
USING (
  bucket_id = 'city-pos' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can delete their own files
CREATE POLICY "Users can delete own files" 
ON storage.objects FOR DELETE 
USING (
  bucket_id = 'city-pos' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Admins can manage all files in public bucket
CREATE POLICY "Admins can manage all public files" 
ON storage.objects FOR ALL 
USING (
  bucket_id = 'city-pos'
  AND EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- =====================================================
-- STORAGE POLICIES FOR PRIVATE BUCKET (city-pos-private)
-- =====================================================

-- Policy: Users can view their own private files
CREATE POLICY "Users can view own private files" 
ON storage.objects FOR SELECT 
USING (
  bucket_id = 'city-pos-private' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Authenticated users can upload private files
CREATE POLICY "Authenticated users can upload private files" 
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'city-pos-private' 
  AND auth.role() = 'authenticated'
);

-- Policy: Users can update their own private files
CREATE POLICY "Users can update own private files" 
ON storage.objects FOR UPDATE 
USING (
  bucket_id = 'city-pos-private' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can delete their own private files
CREATE POLICY "Users can delete own private files" 
ON storage.objects FOR DELETE 
USING (
  bucket_id = 'city-pos-private' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Admins can manage all private files
CREATE POLICY "Admins can manage all private files" 
ON storage.objects FOR ALL 
USING (
  bucket_id = 'city-pos-private'
  AND EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- =====================================================
-- HELPER FUNCTIONS FOR STORAGE
-- =====================================================

-- Function to get storage usage by user
CREATE OR REPLACE FUNCTION get_user_storage_usage(p_user_id UUID)
RETURNS TABLE (
  bucket_name TEXT,
  file_count BIGINT,
  total_size BIGINT,
  avg_size NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sf.bucket_name::TEXT,
    COUNT(*)::BIGINT as file_count,
    COALESCE(SUM(sf.file_size), 0)::BIGINT as total_size,
    COALESCE(AVG(sf.file_size), 0)::NUMERIC as avg_size
  FROM public.storage_files sf
  WHERE sf.uploaded_by = p_user_id
  GROUP BY sf.bucket_name;
END;
$$ language 'plpgsql';

-- Function to get storage usage by file type
CREATE OR REPLACE FUNCTION get_storage_usage_by_type()
RETURNS TABLE (
  file_type TEXT,
  file_count BIGINT,
  total_size BIGINT,
  avg_size NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sf.file_type::TEXT,
    COUNT(*)::BIGINT as file_count,
    COALESCE(SUM(sf.file_size), 0)::BIGINT as total_size,
    COALESCE(AVG(sf.file_size), 0)::NUMERIC as avg_size
  FROM public.storage_files sf
  GROUP BY sf.file_type
  ORDER BY total_size DESC;
END;
$$ language 'plpgsql';

-- Function to clean up unused storage files
CREATE OR REPLACE FUNCTION cleanup_unused_storage_files()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
BEGIN
  -- Delete storage files that are not referenced by any entity
  DELETE FROM public.storage_files 
  WHERE id NOT IN (
    SELECT avatar_file_id FROM public.users WHERE avatar_file_id IS NOT NULL
    UNION
    SELECT image_file_id FROM public.products WHERE image_file_id IS NOT NULL
    -- Add more references as you add more file relationships
  );
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ language 'plpgsql';

-- =====================================================
-- STORAGE FOLDER STRUCTURE FUNCTIONS
-- =====================================================

-- Function to generate organized folder path
CREATE OR REPLACE FUNCTION generate_storage_path(
  p_user_id UUID,
  p_category file_category,
  p_file_extension TEXT DEFAULT '.jpg'
)
RETURNS TEXT AS $$
DECLARE
  folder_path TEXT;
  file_uuid UUID;
BEGIN
  file_uuid := uuid_generate_v4();
  
  -- Generate path: user_id/category/year/month/uuid.extension
  folder_path := p_user_id::TEXT || '/' ||
                 p_category::TEXT || '/' ||
                 EXTRACT(YEAR FROM NOW())::TEXT || '/' ||
                 LPAD(EXTRACT(MONTH FROM NOW())::TEXT, 2, '0') || '/' ||
                 file_uuid::TEXT || p_file_extension;
  
  RETURN folder_path;
END;
$$ language 'plpgsql';

-- =====================================================
-- STORAGE QUOTA MANAGEMENT
-- =====================================================

-- Function to check user storage quota
CREATE OR REPLACE FUNCTION check_user_storage_quota(
  p_user_id UUID,
  p_file_size BIGINT,
  p_quota_limit BIGINT DEFAULT 1073741824 -- 1GB default
)
RETURNS BOOLEAN AS $$
DECLARE
  current_usage BIGINT;
BEGIN
  -- Get current storage usage
  SELECT COALESCE(SUM(file_size), 0) 
  INTO current_usage
  FROM public.storage_files 
  WHERE uploaded_by = p_user_id;
  
  -- Check if adding new file would exceed quota
  RETURN (current_usage + p_file_size) <= p_quota_limit;
END;
$$ language 'plpgsql';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if buckets were created
SELECT 
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types
FROM storage.buckets 
WHERE id IN ('city-pos', 'city-pos-private');

-- Check storage policies
SELECT 
  policyname,
  cmd,
  qual
FROM pg_policies 
WHERE tablename = 'objects' 
  AND schemaname = 'storage'
ORDER BY policyname;

-- Test storage usage functions
SELECT * FROM get_storage_usage_by_type();

-- =====================================================
-- SAMPLE USAGE EXAMPLES
-- =====================================================

/*
-- Example 1: Upload avatar for user
SELECT set_user_avatar(
  (SELECT id FROM public.users LIMIT 1),
  'my-avatar.jpg',
  25600, -- 25KB
  'image/jpeg',
  200, -- width
  200, -- height
  'User profile picture'
);

-- Example 2: Upload product image
SELECT set_product_image(
  (SELECT id FROM public.products LIMIT 1),
  'coffee-image.jpg',
  102400, -- 100KB
  'image/jpeg',
  800, -- width
  600, -- height
  'Delicious coffee product image',
  (SELECT id FROM public.users LIMIT 1)
);

-- Example 3: Check storage usage
SELECT * FROM get_user_storage_usage((SELECT id FROM public.users LIMIT 1));

-- Example 4: Generate storage path
SELECT generate_storage_path(
  (SELECT id FROM public.users LIMIT 1),
  'product',
  '.png'
);

-- Example 5: Check quota
SELECT check_user_storage_quota(
  (SELECT id FROM public.users LIMIT 1),
  1048576, -- 1MB file
  1073741824 -- 1GB quota
);
*/
