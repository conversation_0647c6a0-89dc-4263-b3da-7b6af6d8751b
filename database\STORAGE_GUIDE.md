# Hướng Dẫn Sử Dụng Storage System - City POS

## Tổng <PERSON>uan

Hệ thống storage của City POS được thiết kế để quản lý files (hình ảnh, tài liệu) một cách an toàn và hiệu quả với Supabase Storage integration.

## Cấu Trúc Storage

### 1. Bảng `storage_files` - Quản Lý Metadata Files
```sql
- id (UUID) - Khóa chính
- file_name (VARCHAR) - Tên file gốc
- file_path (TEXT) - Đường dẫn đầy đủ trong Supabase Storage
- bucket_name (VARCHAR) - Tên bucket storage
- file_type (ENUM) - Loại file: 'image', 'document', 'video', 'audio', 'other'
- file_category (ENUM) - Danh mục: 'avatar', 'product', 'invoice', 'receipt', 'document', 'other'
- mime_type (VARCHAR) - <PERSON>ại MIME (image/jpeg, image/png...)
- file_size (BIGINT) - <PERSON>ích thước file (bytes)
- width, height (INTEGER) - <PERSON><PERSON><PERSON> thước hình ảnh (pixels)
- entity_type, entity_id - Liên kết với bảng khác
- is_public (BOOLEAN) - File công khai hay riêng tư
- access_url (TEXT) - URL truy cập công khai
- uploaded_by (UUID) - Người upload
```

### 2. Storage Buckets

#### `city-pos` (Public Bucket)
- **Mục đích**: Lưu files công khai (avatar, product images)
- **Giới hạn**: 50MB per file
- **MIME types**: image/jpeg, image/png, image/gif, image/webp, application/pdf
- **Truy cập**: Public read, authenticated write

#### `city-pos-private` (Private Bucket)  
- **Mục đích**: Lưu files riêng tư (invoices, receipts, documents)
- **Giới hạn**: 100MB per file
- **MIME types**: PDF, images, Office documents
- **Truy cập**: Owner only

## Cách Sử Dụng

### 1. Upload Avatar Cho User

```sql
-- Sử dụng function helper
SELECT set_user_avatar(
  'user-uuid-here',
  'my-avatar.jpg',
  25600, -- 25KB
  'image/jpeg',
  200, -- width
  200, -- height
  'User profile picture'
);
```

### 2. Upload Hình Ảnh Sản Phẩm

```sql
-- Sử dụng function helper
SELECT set_product_image(
  'product-uuid-here',
  'coffee-image.jpg',
  102400, -- 100KB
  'image/jpeg',
  800, -- width
  600, -- height
  'Delicious coffee product image',
  'uploader-user-uuid'
);
```

### 3. Upload File Thủ Công

```sql
-- Tạo record storage_files trực tiếp
INSERT INTO public.storage_files (
  file_name, file_type, file_category, mime_type, file_size,
  entity_type, entity_id, is_public, uploaded_by
) VALUES (
  'invoice-001.pdf', 'document', 'invoice', 'application/pdf', 245760,
  'invoice', 'invoice-uuid-here', false, 'user-uuid-here'
);
```

## Cấu Trúc Thư Mục

Storage files được tổ chức theo cấu trúc:
```
bucket/
├── user-id/
│   ├── avatar/
│   │   └── 2024/
│   │       └── 01/
│   │           └── uuid.jpg
│   ├── product/
│   │   └── 2024/
│   │       └── 01/
│   │           └── uuid.jpg
│   └── document/
│       └── 2024/
│           └── 01/
│               └── uuid.pdf
```

## Phân Quyền (RLS Policies)

### Public Bucket (`city-pos`)
- **View**: Tất cả mọi người
- **Upload**: User đã đăng nhập
- **Update/Delete**: Chỉ owner hoặc admin

### Private Bucket (`city-pos-private`)
- **View**: Chỉ owner hoặc admin
- **Upload**: User đã đăng nhập
- **Update/Delete**: Chỉ owner hoặc admin

## Functions Hữu Ích

### 1. Kiểm Tra Storage Usage

```sql
-- Usage theo user
SELECT * FROM get_user_storage_usage('user-uuid-here');

-- Usage theo file type
SELECT * FROM get_storage_usage_by_type();
```

### 2. Quản Lý Quota

```sql
-- Kiểm tra quota trước khi upload
SELECT check_user_storage_quota(
  'user-uuid-here',
  1048576, -- 1MB file size
  1073741824 -- 1GB quota limit
);
```

### 3. Cleanup Files

```sql
-- Xóa files không được sử dụng
SELECT cleanup_unused_storage_files();

-- Xóa files orphaned
SELECT cleanup_orphaned_files();
```

### 4. Migration Từ URL Cũ

```sql
-- Migrate avatar URLs hiện tại
SELECT migrate_avatar_urls();

-- Migrate product image URLs hiện tại  
SELECT migrate_product_image_urls();
```

## Tích Hợp Với Flutter

### 1. Upload File

```dart
// Upload file và tạo storage record
final file = File('path/to/image.jpg');
final fileName = 'avatar-${user.id}.jpg';
final storagePath = await supabase.storage
  .from('city-pos')
  .upload('${user.id}/avatar/2024/01/$fileName', file);

// Tạo storage_files record
await supabase.from('storage_files').insert({
  'file_name': fileName,
  'file_path': storagePath,
  'bucket_name': 'city-pos',
  'file_type': 'image',
  'file_category': 'avatar',
  'mime_type': 'image/jpeg',
  'file_size': await file.length(),
  'entity_type': 'user',
  'entity_id': user.id,
  'is_public': true,
  'uploaded_by': user.id,
});
```

### 2. Get File URL

```dart
// Lấy URL từ storage_files
final fileRecord = await supabase
  .from('storage_files')
  .select()
  .eq('entity_type', 'user')
  .eq('entity_id', user.id)
  .eq('file_category', 'avatar')
  .single();

String imageUrl;
if (fileRecord['is_public']) {
  imageUrl = fileRecord['access_url'];
} else {
  // Tạo signed URL cho private files
  imageUrl = await supabase.storage
    .from(fileRecord['bucket_name'])
    .createSignedUrl(fileRecord['file_path'], 3600); // 1 hour
}
```

### 3. Sử dụng Views Để Backward Compatibility

```dart
// Sử dụng view để lấy cả URL cũ và mới
final users = await supabase
  .from('users_with_avatar')
  .select();

// users sẽ có cả avatar_url và avatar_url_computed
```

## Best Practices

### 1. Naming Convention
- **Avatar**: `avatar-{user_id}-{timestamp}.{ext}`
- **Product**: `product-{product_id}-{timestamp}.{ext}`
- **Document**: `{type}-{entity_id}-{timestamp}.{ext}`

### 2. File Size Limits
- **Avatar**: Tối đa 2MB
- **Product images**: Tối đa 5MB
- **Documents**: Tối đa 50MB

### 3. Image Optimization
- Resize images trước khi upload
- Sử dụng WebP format khi có thể
- Tạo thumbnails cho images lớn

### 4. Security
- Luôn validate file type và size
- Scan files cho malware nếu cần
- Sử dụng signed URLs cho private files

## Troubleshooting

### 1. File Upload Fails
- Kiểm tra file size limit
- Kiểm tra MIME type allowed
- Kiểm tra storage quota

### 2. Access Denied
- Kiểm tra RLS policies
- Kiểm tra bucket permissions
- Kiểm tra user authentication

### 3. Orphaned Files
- Chạy cleanup functions định kỳ
- Monitor storage usage
- Implement file lifecycle management

## Migration Plan

### Phase 1: Setup Storage System
1. Chạy `05_storage_files.sql`
2. Chạy `06_update_tables_for_storage.sql`
3. Chạy `07_supabase_storage_setup.sql`

### Phase 2: Migrate Existing Data
1. Chạy `migrate_avatar_urls()`
2. Chạy `migrate_product_image_urls()`
3. Verify migration results

### Phase 3: Update Application Code
1. Update upload logic to use storage_files
2. Update display logic to use new URLs
3. Test thoroughly

### Phase 4: Cleanup
1. Remove old avatar_url và image_url columns (optional)
2. Update views if needed
3. Monitor storage usage
