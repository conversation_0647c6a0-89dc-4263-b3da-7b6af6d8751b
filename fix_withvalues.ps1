# PowerShell script to replace .withValues(alpha: X) with .withOpacity(X)
Get-ChildItem -Path "lib" -Recurse -Filter "*.dart" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $newContent = $content -replace '\.withValues\(alpha:\s*([^)]+)\)', '.withOpacity($1)'
    if ($content -ne $newContent) {
        Set-Content -Path $_.FullName -Value $newContent -NoNewline
        Write-Host "Updated: $($_.FullName)"
    }
}
Write-Host "Replacement completed!"
