import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/partners/data/services/partner_service.dart';
import '../../features/partners/domain/entities/partner.dart';

// Partner state
class PartnerState {
  final List<Partner> partners;
  final bool isLoading;
  final String? error;
  final String? searchQuery;

  const PartnerState({
    this.partners = const [],
    this.isLoading = false,
    this.error,
    this.searchQuery,
  });

  PartnerState copyWith({
    List<Partner> partners = const [],
    bool isLoading = false,
    String? error,
    String? searchQuery,
  }) {
    return PartnerState(
      partners: partners,
      isLoading: isLoading,
      error: error ?? this.error,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

// Partner notifier
class PartnerNotifier extends StateNotifier<PartnerState> {
  PartnerNotifier() : super(const PartnerState()) {
    loadPartners();
  }

  Future<void> loadPartners() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final partners = await PartnerService.getPartners(
        searchQuery: state.searchQuery,
        type: PartnerType.customer,
        isActive: true,
      );

      state = state.copyWith(partners: partners, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> searchPartners(String query) async {
    state = state.copyWith(searchQuery: query);
    await loadPartners();
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void clearSearch() {
    state = state.copyWith(searchQuery: null);
    loadPartners();
  }
}

// Providers
final partnersProvider = StateNotifierProvider<PartnerNotifier, PartnerState>((
  ref,
) {
  return PartnerNotifier();
});

// Convenience providers
final partnersListProvider = Provider<List<Partner>>((ref) {
  return ref.watch(partnersProvider.select((state) => state.partners));
});

final isPartnersLoadingProvider = Provider<bool>((ref) {
  return ref.watch(partnersProvider.select((state) => state.isLoading));
});

final partnerErrorProvider = Provider<String?>((ref) {
  return ref.watch(partnersProvider.select((state) => state.error));
});
