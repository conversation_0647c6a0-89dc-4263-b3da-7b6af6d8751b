import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/order.dart';
import '../../data/models/order_item.dart';
import '../../data/models/partner.dart';
import '../../data/models/product.dart';
import '../services/sales_service.dart';

// Sales state
class SalesState {
  final List<Order> orders;
  final List<Partner> partners;
  final Order? currentOrder;
  final bool isLoading;
  final String? error;
  final Map<String, dynamic>? salesStats;

  const SalesState({
    this.orders = const [],
    this.partners = const [],
    this.currentOrder,
    this.isLoading = false,
    this.error,
    this.salesStats,
  });

  SalesState copyWith({
    List<Order>? orders,
    List<Partner>? partners,
    Order? currentOrder,
    bool? isLoading,
    String? error,
    Map<String, dynamic>? salesStats,
  }) {
    return SalesState(
      orders: orders ?? this.orders,
      partners: partners ?? this.partners,
      currentOrder: currentOrder ?? this.currentOrder,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      salesStats: salesStats ?? this.salesStats,
    );
  }
}

// Sales notifier
class SalesNotifier extends StateNotifier<SalesState> {
  SalesNotifier() : super(const SalesState()) {
    loadOrders();
    loadPartners();
    loadSalesStats();
  }

  Future<void> loadOrders() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final orders = await SalesService.getOrders();
      state = state.copyWith(orders: orders, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> loadPartners() async {
    try {
      final partners = await SalesService.getPartners(type: 'customer');
      state = state.copyWith(partners: partners);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> loadSalesStats() async {
    try {
      final stats = await SalesService.getSalesAnalytics();
      state = state.copyWith(salesStats: stats);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<bool> createOrder(Order order) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final newOrder = await SalesService.createOrder(order);

      state = state.copyWith(
        orders: [newOrder, ...state.orders],
        isLoading: false,
      );

      // Reload stats
      loadSalesStats();

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  Future<bool> updateOrder(String id, Order order) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final updatedOrder = await SalesService.updateOrder(id, order);

      final updatedOrders = state.orders.map((o) {
        return o.id == id ? updatedOrder : o;
      }).toList();

      state = state.copyWith(orders: updatedOrders, isLoading: false);

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  Future<bool> updateOrderStatus(String id, String status) async {
    try {
      final updatedOrder = await SalesService.updateOrderStatus(id, status);

      final updatedOrders = state.orders.map((o) {
        return o.id == id ? updatedOrder : o;
      }).toList();

      state = state.copyWith(orders: updatedOrders);

      // Reload stats
      loadSalesStats();

      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> updatePaymentStatus(String id, String paymentStatus) async {
    try {
      final updatedOrder = await SalesService.updatePaymentStatus(
        id,
        paymentStatus,
      );

      final updatedOrders = state.orders.map((o) {
        return o.id == id ? updatedOrder : o;
      }).toList();

      state = state.copyWith(orders: updatedOrders);

      // Reload stats
      loadSalesStats();

      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> deleteOrder(String id) async {
    try {
      await SalesService.deleteOrder(id);

      final updatedOrders = state.orders.where((o) => o.id != id).toList();

      state = state.copyWith(orders: updatedOrders);

      // Reload stats
      loadSalesStats();

      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  // Current order management
  void startNewOrder() {
    state = state.copyWith(
      currentOrder: const Order(
        type: 'sale',
        status: 'pending',
        paymentStatus: 'pending',
        items: [],
      ),
    );
  }

  void setCurrentOrder(Order? order) {
    state = state.copyWith(currentOrder: order);
  }

  void addItemToCurrentOrder(
    Product product, {
    int quantity = 1,
    double? customPrice,
  }) {
    if (state.currentOrder == null) return;

    final existingItemIndex = state.currentOrder!.items.indexWhere(
      (item) => item.productId == product.id,
    );

    List<OrderItem> updatedItems;

    if (existingItemIndex >= 0) {
      // Update existing item quantity
      final existingItem = state.currentOrder!.items[existingItemIndex];
      final newQuantity = existingItem.quantity + quantity;
      final updatedItem = existingItem.copyWithQuantity(newQuantity);

      updatedItems = List.from(state.currentOrder!.items);
      updatedItems[existingItemIndex] = updatedItem;
    } else {
      // Add new item
      final newItem = OrderItem.fromProduct(
        product,
        quantity: quantity,
        customPrice: customPrice,
      );
      updatedItems = [...state.currentOrder!.items, newItem];
    }

    final updatedOrder = state.currentOrder!.copyWith(items: updatedItems);
    final calculatedOrder = _calculateOrderTotals(updatedOrder);

    state = state.copyWith(currentOrder: calculatedOrder);
  }

  void updateItemInCurrentOrder(int itemIndex, OrderItem updatedItem) {
    if (state.currentOrder == null ||
        itemIndex < 0 ||
        itemIndex >= state.currentOrder!.items.length) {
      return;
    }

    final updatedItems = List<OrderItem>.from(state.currentOrder!.items);
    updatedItems[itemIndex] = updatedItem.copyWithCalculatedTotal();

    final updatedOrder = state.currentOrder!.copyWith(items: updatedItems);
    final calculatedOrder = _calculateOrderTotals(updatedOrder);

    state = state.copyWith(currentOrder: calculatedOrder);
  }

  void removeItemFromCurrentOrder(int itemIndex) {
    if (state.currentOrder == null ||
        itemIndex < 0 ||
        itemIndex >= state.currentOrder!.items.length) {
      return;
    }

    final updatedItems = List<OrderItem>.from(state.currentOrder!.items);
    updatedItems.removeAt(itemIndex);

    final updatedOrder = state.currentOrder!.copyWith(items: updatedItems);
    final calculatedOrder = _calculateOrderTotals(updatedOrder);

    state = state.copyWith(currentOrder: calculatedOrder);
  }

  void updateCurrentOrderPartner(Partner? partner) {
    if (state.currentOrder == null) return;

    final updatedOrder = state.currentOrder!.copyWith(
      partnerId: partner?.id,
      partner: partner,
    );

    state = state.copyWith(currentOrder: updatedOrder);
  }

  void updateCurrentOrderDiscount(double discountAmount) {
    if (state.currentOrder == null) return;

    final updatedOrder = state.currentOrder!.copyWith(
      discountAmount: discountAmount,
    );
    final calculatedOrder = _calculateOrderTotals(updatedOrder);

    state = state.copyWith(currentOrder: calculatedOrder);
  }

  void updateCurrentOrderTax(double taxAmount) {
    if (state.currentOrder == null) return;

    final updatedOrder = state.currentOrder!.copyWith(taxAmount: taxAmount);
    final calculatedOrder = _calculateOrderTotals(updatedOrder);

    state = state.copyWith(currentOrder: calculatedOrder);
  }

  void updateCurrentOrderNotes(String notes) {
    if (state.currentOrder == null) return;

    final updatedOrder = state.currentOrder!.copyWith(notes: notes);
    state = state.copyWith(currentOrder: updatedOrder);
  }

  void clearCurrentOrder() {
    state = state.copyWith(currentOrder: null);
  }

  Order _calculateOrderTotals(Order order) {
    final subtotal = order.items.fold(
      0.0,
      (sum, item) => sum + item.totalAmount,
    );
    final total = subtotal + order.taxAmount - order.discountAmount;

    return order.copyWith(subtotal: subtotal, totalAmount: total);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Providers
final salesProvider = StateNotifierProvider<SalesNotifier, SalesState>((ref) {
  return SalesNotifier();
});

// Convenience providers
final ordersListProvider = Provider<List<Order>>((ref) {
  return ref.watch(salesProvider.select((state) => state.orders));
});

final partnersListProvider = Provider<List<Partner>>((ref) {
  return ref.watch(salesProvider.select((state) => state.partners));
});

final currentOrderProvider = Provider<Order?>((ref) {
  return ref.watch(salesProvider.select((state) => state.currentOrder));
});

final salesStatsProvider = Provider<Map<String, dynamic>?>((ref) {
  return ref.watch(salesProvider.select((state) => state.salesStats));
});

final isSalesLoadingProvider = Provider<bool>((ref) {
  return ref.watch(salesProvider.select((state) => state.isLoading));
});

// Order by ID provider
final orderByIdProvider = FutureProvider.family<Order?, String>((
  ref,
  id,
) async {
  return await SalesService.getOrderById(id);
});

// Top selling products provider
final topSellingProductsProvider = FutureProvider<List<Map<String, dynamic>>>((
  ref,
) async {
  return await SalesService.getTopSellingProducts();
});
