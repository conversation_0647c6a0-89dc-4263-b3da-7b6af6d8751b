import 'package:supabase_flutter/supabase_flutter.dart';

import '../config/supabase_config.dart';

class AuthService {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Demo users for testing
  static final Map<String, Map<String, dynamic>> _demoUsers = {
    '<EMAIL>': {
      'id': 'demo-admin-id',
      'email': '<EMAIL>',
      'password': 'admin123',
      'full_name': 'Admin User',
      'phone': '+1234567890',
      'role': 'admin',
      'is_active': true,
    },
    '<EMAIL>': {
      'id': 'demo-manager-id',
      'email': '<EMAIL>',
      'password': 'manager123',
      'full_name': 'Manager User',
      'phone': '+1234567891',
      'role': 'manager',
      'is_active': true,
    },
    '<EMAIL>': {
      'id': 'demo-user-id',
      'email': '<EMAIL>',
      'password': 'user123',
      'full_name': 'Regular User',
      'phone': '+1234567892',
      'role': 'user',
      'is_active': true,
    },
  };

  static User? _currentDemoUser;
  static Map<String, dynamic>? _currentDemoProfile;

  // Get current user
  static User? get currentUser {
    if (SupabaseConfig.isDemoMode) {
      return _currentDemoUser;
    }
    return _supabase.auth.currentUser;
  }

  // Check if user is logged in
  static bool get isLoggedIn => currentUser != null;

  // Get auth state stream
  static Stream<AuthState> get authStateChanges {
    if (SupabaseConfig.isDemoMode) {
      // Return empty stream for demo mode
      return const Stream.empty();
    }
    return _supabase.auth.onAuthStateChange;
  }

  // Sign up with email and password
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String fullName,
    String? phone,
  }) async {
    if (SupabaseConfig.isDemoMode) {
      return _demoSignUp(
        email: email,
        password: password,
        fullName: fullName,
        phone: phone,
      );
    }

    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName, 'phone': phone},
      );

      // If signup successful and user is confirmed, create user profile
      if (response.user != null) {
        await _createUserProfile(
          userId: response.user!.id,
          email: email,
          fullName: fullName,
          phone: phone,
        );
      }

      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Demo sign up
  static Future<AuthResponse> _demoSignUp({
    required String email,
    required String password,
    required String fullName,
    String? phone,
  }) async {
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate network delay

    if (_demoUsers.containsKey(email)) {
      throw Exception('Email already exists');
    }

    // Create new demo user
    final userId = 'demo-user-${DateTime.now().millisecondsSinceEpoch}';
    final newUser = {
      'id': userId,
      'email': email,
      'password': password,
      'full_name': fullName,
      'phone': phone,
      'role': 'user',
      'is_active': true,
    };

    _demoUsers[email] = newUser;

    // Create mock User object
    _currentDemoUser = User(
      id: userId,
      appMetadata: {},
      userMetadata: {'full_name': fullName, 'phone': phone},
      aud: 'authenticated',
      email: email,
      createdAt: DateTime.now().toIso8601String(),
      emailConfirmedAt: DateTime.now().toIso8601String(),
      lastSignInAt: DateTime.now().toIso8601String(),
      role: 'authenticated',
      updatedAt: DateTime.now().toIso8601String(),
    );

    _currentDemoProfile = Map<String, dynamic>.from(newUser);

    // Create mock AuthResponse
    return AuthResponse(
      user: _currentDemoUser,
      session: Session(
        accessToken: 'demo-access-token',
        refreshToken: 'demo-refresh-token',
        expiresIn: 3600,
        tokenType: 'bearer',
        user: _currentDemoUser!,
      ),
    );
  }

  // Sign in with email and password
  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    if (SupabaseConfig.isDemoMode) {
      return _demoSignIn(email: email, password: password);
    }

    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      // If user doesn't exist, create it automatically for testing
      if (e.toString().contains('Invalid login credentials') ||
          e.toString().contains('invalid_credentials')) {
        print('🔧 User not found, creating user: $email');

        try {
          // Try to sign up the user first
          final signUpResponse = await _supabase.auth.signUp(
            email: email,
            password: password,
            data: {'full_name': 'Auto Created User'},
          );

          if (signUpResponse.user != null) {
            print('✅ User created successfully');

            // Create user profile
            await _createUserProfile(
              userId: signUpResponse.user!.id,
              email: email,
              fullName: 'Auto Created User',
            );

            // If user is immediately confirmed, return the session
            if (signUpResponse.session != null) {
              return signUpResponse;
            } else {
              // If email confirmation is required, try to sign in again
              print('🔄 Attempting to sign in after user creation...');
              return await _supabase.auth.signInWithPassword(
                email: email,
                password: password,
              );
            }
          }
        } catch (signUpError) {
          print('❌ Failed to create user: $signUpError');
        }
      }

      rethrow;
    }
  }

  // Demo sign in
  static Future<AuthResponse> _demoSignIn({
    required String email,
    required String password,
  }) async {
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate network delay

    final demoUser = _demoUsers[email];
    if (demoUser == null || demoUser['password'] != password) {
      throw Exception('Invalid email or password');
    }

    // Create mock User object
    _currentDemoUser = User(
      id: demoUser['id'],
      appMetadata: {},
      userMetadata: {
        'full_name': demoUser['full_name'],
        'phone': demoUser['phone'],
      },
      aud: 'authenticated',
      email: demoUser['email'],
      createdAt: DateTime.now().toIso8601String(),
      emailConfirmedAt: DateTime.now().toIso8601String(),
      lastSignInAt: DateTime.now().toIso8601String(),
      role: 'authenticated',
      updatedAt: DateTime.now().toIso8601String(),
    );

    _currentDemoProfile = Map<String, dynamic>.from(demoUser);

    // Create mock AuthResponse
    return AuthResponse(
      user: _currentDemoUser,
      session: Session(
        accessToken: 'demo-access-token',
        refreshToken: 'demo-refresh-token',
        expiresIn: 3600,
        tokenType: 'bearer',
        user: _currentDemoUser!,
      ),
    );
  }

  // Sign out
  static Future<void> signOut() async {
    if (SupabaseConfig.isDemoMode) {
      _currentDemoUser = null;
      _currentDemoProfile = null;
      return;
    }

    try {
      await _supabase.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  // Reset password
  static Future<void> resetPassword({required String email}) async {
    if (SupabaseConfig.isDemoMode) {
      // Simulate password reset in demo mode
      await Future.delayed(const Duration(milliseconds: 500));
      return;
    }

    try {
      await _supabase.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }

  // Update password
  static Future<UserResponse> updatePassword({
    required String newPassword,
  }) async {
    if (SupabaseConfig.isDemoMode) {
      // Simulate password update in demo mode
      await Future.delayed(const Duration(milliseconds: 500));
      return UserResponse.fromJson({'user': _currentDemoUser?.toJson()});
    }

    try {
      final response = await _supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Update user profile
  static Future<UserResponse> updateProfile({
    String? fullName,
    String? phone,
  }) async {
    if (SupabaseConfig.isDemoMode) {
      // Update demo profile
      if (_currentDemoProfile != null) {
        if (fullName != null) _currentDemoProfile!['full_name'] = fullName;
        if (phone != null) _currentDemoProfile!['phone'] = phone;
      }
      await Future.delayed(const Duration(milliseconds: 500));
      return UserResponse.fromJson({'user': _currentDemoUser?.toJson()});
    }

    try {
      final response = await _supabase.auth.updateUser(
        UserAttributes(
          data: {
            if (fullName != null) 'full_name': fullName,
            if (phone != null) 'phone': phone,
          },
        ),
      );

      // Also update in users table
      if (response.user != null) {
        await _updateUserProfile(
          userId: response.user!.id,
          fullName: fullName,
          phone: phone,
        );
      }

      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Get user profile from users table
  static Future<Map<String, dynamic>?> getUserProfile() async {
    if (SupabaseConfig.isDemoMode) {
      return _currentDemoProfile;
    }

    try {
      if (currentUser == null) return null;

      final response = await _supabase
          .from('users')
          .select()
          .eq('id', currentUser!.id)
          .maybeSingle();

      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Create user profile in users table
  static Future<void> _createUserProfile({
    required String userId,
    required String email,
    required String fullName,
    String? phone,
  }) async {
    if (SupabaseConfig.isDemoMode) {
      // Skip profile creation in demo mode
      return;
    }

    try {
      await _supabase.from('users').insert({
        'id': userId,
        'email': email,
        'full_name': fullName,
        'phone': phone,
        'role': 'user', // Default role
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // Don't throw error if profile creation fails
      // User can still login and profile can be created later
      // Error creating user profile: $e
    }
  }

  // Update user profile in users table
  static Future<void> _updateUserProfile({
    required String userId,
    String? fullName,
    String? phone,
  }) async {
    if (SupabaseConfig.isDemoMode) {
      // Skip profile update in demo mode
      return;
    }

    try {
      await _supabase
          .from('users')
          .update({
            if (fullName != null) 'full_name': fullName,
            if (phone != null) 'phone': phone,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);
    } catch (e) {
      // Error updating user profile: $e
    }
  }

  // Check if email exists
  static Future<bool> checkEmailExists(String email) async {
    if (SupabaseConfig.isDemoMode) {
      return _demoUsers.containsKey(email);
    }

    try {
      final response = await _supabase
          .from('users')
          .select('email')
          .eq('email', email)
          .maybeSingle();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  // Get user role
  static Future<String?> getUserRole() async {
    if (SupabaseConfig.isDemoMode) {
      return _currentDemoProfile?['role'];
    }

    try {
      if (currentUser == null) return null;

      final response = await _supabase
          .from('users')
          .select('role')
          .eq('id', currentUser!.id)
          .maybeSingle();

      return response?['role'];
    } catch (e) {
      return null;
    }
  }

  // Check if user has permission
  static Future<bool> hasPermission(String permission) async {
    try {
      final role = await getUserRole();
      if (role == null) return false;

      // Admin has all permissions
      if (role == 'admin') return true;

      // Manager has most permissions
      if (role == 'manager') {
        return !['user_management', 'system_settings'].contains(permission);
      }

      // User has limited permissions
      if (role == 'user') {
        return ['sales', 'inventory_view', 'reports_view'].contains(permission);
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // Refresh session
  static Future<AuthResponse?> refreshSession() async {
    if (SupabaseConfig.isDemoMode) {
      // Return current demo session
      if (_currentDemoUser != null) {
        return AuthResponse(
          user: _currentDemoUser,
          session: Session(
            accessToken: 'demo-access-token',
            refreshToken: 'demo-refresh-token',
            expiresIn: 3600,
            tokenType: 'bearer',
            user: _currentDemoUser!,
          ),
        );
      }
      return null;
    }

    try {
      final response = await _supabase.auth.refreshSession();
      return response;
    } catch (e) {
      return null;
    }
  }
}
