import 'package:flutter_riverpod/flutter_riverpod.dart';

// Global dashboard refresh service
class DashboardRefreshService {
  static WidgetRef? _ref;

  // Initialize with ref from main app
  static void initialize(WidgetRef ref) {
    _ref = ref;
    print('🔥 DashboardRefreshService initialized');
  }

  // Trigger dashboard refresh from anywhere in the app
  static void refreshDashboard({String? reason}) {
    if (_ref != null) {
      print('🔥 Dashboard refresh triggered globally${reason != null ? ' - Reason: $reason' : ''}');
      try {
        _ref!.read(dashboardRefreshProvider.notifier).refresh();
      } catch (e) {
        print('🔥 Error triggering dashboard refresh: $e');
      }
    } else {
      print('🔥 DashboardRefreshService not initialized - cannot refresh');
    }
  }

  // Trigger refresh when order is created
  static void onOrderCreated(String orderNumber) {
    refreshDashboard(reason: 'Order created: $orderNumber');
  }

  // Trigger refresh when customer is added
  static void onCustomerAdded(String customerName) {
    refreshDashboard(reason: 'Customer added: $customerName');
  }

  // Trigger refresh when product stock is updated
  static void onStockUpdated(String productName) {
    refreshDashboard(reason: 'Stock updated: $productName');
  }

  // Trigger refresh when product is added
  static void onProductAdded(String productName) {
    refreshDashboard(reason: 'Product added: $productName');
  }

  // Trigger refresh when invoice is created
  static void onInvoiceCreated(String invoiceNumber) {
    refreshDashboard(reason: 'Invoice created: $invoiceNumber');
  }

  // Trigger refresh when payment is received
  static void onPaymentReceived(double amount) {
    refreshDashboard(reason: 'Payment received: ${amount.toStringAsFixed(0)}₫');
  }

  // Trigger refresh when finance transaction is added
  static void onFinanceTransactionAdded(String type, double amount) {
    refreshDashboard(reason: 'Finance transaction: $type ${amount.toStringAsFixed(0)}₫');
  }

  // Trigger refresh when partner is added
  static void onPartnerAdded(String partnerName, String type) {
    refreshDashboard(reason: 'Partner added: $partnerName ($type)');
  }

  // Trigger refresh when data is imported
  static void onDataImported(String dataType) {
    refreshDashboard(reason: 'Data imported: $dataType');
  }

  // Trigger refresh when settings are changed
  static void onSettingsChanged(String setting) {
    refreshDashboard(reason: 'Settings changed: $setting');
  }

  // Manual refresh with custom reason
  static void manualRefresh([String? customReason]) {
    refreshDashboard(reason: customReason ?? 'Manual refresh');
  }
}

// Import the dashboard refresh provider
// This will be imported from dashboard_screen.dart
final dashboardRefreshProvider = StateNotifierProvider<DashboardRefreshNotifier, int>((ref) {
  return DashboardRefreshNotifier();
});

class DashboardRefreshNotifier extends StateNotifier<int> {
  DashboardRefreshNotifier() : super(0);

  void refresh() {
    print('🔥 Dashboard refresh triggered');
    state = state + 1;
  }

  void autoRefresh() {
    print('🔥 Dashboard auto refresh triggered');
    state = state + 1;
  }
}
