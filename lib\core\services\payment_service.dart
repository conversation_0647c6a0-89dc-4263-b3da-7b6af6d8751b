import 'package:supabase_flutter/supabase_flutter.dart';

import '../../data/models/order.dart';
import '../../features/notifications/data/services/notification_service.dart';
import '../config/supabase_config.dart';
import 'push_notification_service.dart';

class PaymentService {
  static final SupabaseClient _supabase = SupabaseConfig.client;

  // Create finance receipt from order
  static Future<void> createReceiptFromOrder(Order order) async {
    try {
      print('=== FINANCE DEBUG ===');
      print('Original Type: receipt');
      print('Category: sales');
      print('Amount: ${order.totalAmount}');
      print('Mapped DB Type: income');

      print(
        '✅ Đã ghi nhận phiếu thu từ đơn hàng ${order.orderNumber} - ${order.totalAmount}đ',
      );

      // Real Supabase implementation - use payments table
      final transactionData = {
        'payment_number':
            'PT${order.orderNumber?.substring(2) ?? DateTime.now().millisecondsSinceEpoch}',
        'type': 'income',
        'category': 'sales',
        'amount': order.totalAmount,
        'payment_method': order.paymentMethod ?? 'cash',
        'reference_type': 'order',
        'reference_id': order.id,
        'description': 'Thu từ đơn hàng ${order.orderNumber}',
        'payment_date': DateTime.now().toIso8601String().split(
          'T',
        )[0], // YYYY-MM-DD format
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase.from('payments').insert(transactionData);

      // Send push notification for payment
      await _sendPaymentNotification(
        order,
        transactionData['payment_number'] as String,
      );
    } catch (e) {
      print('Error creating receipt: $e');
      throw Exception('Lỗi tạo phiếu thu: $e');
    }
  }

  // Send push notification for payment
  static Future<void> _sendPaymentNotification(
    Order order,
    String paymentNumber,
  ) async {
    try {
      // Send push notification
      await PushNotificationService.sendPaymentNotification(
        orderNumber: order.orderNumber ?? 'N/A',
        amount: order.totalAmount,
        paymentMethod: order.paymentMethod ?? 'Tiền mặt',
      );

      // Also create in-app notification
      await NotificationService.notifyPaymentReceived(
        order.orderNumber ?? 'N/A',
        order.totalAmount,
        order.paymentMethod ?? 'Tiền mặt',
      );
    } catch (e) {
      print('❌ Lỗi gửi thông báo thanh toán: $e');
      // Don't throw error to avoid breaking the payment creation
    }
  }
}
