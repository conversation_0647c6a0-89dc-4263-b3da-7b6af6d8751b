import 'dart:io';

import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';

import '../../data/models/order.dart';

class PDFService {
  // Font cache for better performance
  static pw.Font? _cachedFont;
  static bool _fontLoadAttempted = false;

  // Load font with fallback system for global app
  static Future<pw.Font?> _loadFont() async {
    if (_fontLoadAttempted && _cachedFont != null) {
      return _cachedFont;
    }

    _fontLoadAttempted = true;

    // Font priority list for global support
    final fontPaths = [
      'assets/fonts/NotoSans-Regular.ttf', // Best for global/multilingual
      'assets/fonts/Roboto-Regular.ttf', // Fallback 1
      'packages/pdf/fonts/open_sans.ttf', // Fallback 2 (system)
    ];

    for (final fontPath in fontPaths) {
      try {
        final fontData = await rootBundle.load(fontPath);
        _cachedFont = pw.Font.ttf(fontData);
        print('✅ Font loaded successfully: $fontPath');
        return _cachedFont;
      } catch (e) {
        print('❌ Font loading failed for $fontPath: $e');
        continue;
      }
    }

    print('⚠️ All fonts failed to load, using default font');
    return null; // Use default font as last resort
  }

  // Generate and share invoice PDF
  Future<void> generateAndShareInvoice(Order order) async {
    try {
      final pdfFile = await generateInvoicePDF(order);

      await Share.shareXFiles(
        [XFile(pdfFile.path)],
        text: 'Hóa đơn ${order.orderNumber ?? 'N/A'}',
        subject: 'Hóa đơn bán hàng - City POS',
      );
    } catch (e) {
      throw Exception('Lỗi khi tạo và chia sẻ PDF: $e');
    }
  }

  // Generate invoice PDF from order with font fallback system
  static Future<File> generateInvoicePDF(Order order) async {
    try {
      final pdf = pw.Document();

      // Load font with fallback system
      final font = await _loadFont();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(font),
                pw.SizedBox(height: 20),

                // Invoice info
                _buildInvoiceInfo(order, font),
                pw.SizedBox(height: 20),

                // Customer info
                _buildCustomerInfo(order, font),
                pw.SizedBox(height: 20),

                // Items table
                _buildItemsTable(order, font),
                pw.SizedBox(height: 20),

                // Total
                _buildTotal(order, font),
                pw.SizedBox(height: 30),

                // Footer
                _buildFooter(font),
              ],
            );
          },
        ),
      );

      // Save PDF to file
      final output = await getTemporaryDirectory();
      final file = File(
        '${output.path}/invoice_${order.orderNumber ?? DateTime.now().millisecondsSinceEpoch}.pdf',
      );
      await file.writeAsBytes(await pdf.save());

      return file;
    } catch (e) {
      throw Exception('Lỗi khi tạo PDF: $e');
    }
  }

  // Build methods with font support
  static pw.Widget _buildHeader(pw.Font? font) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            'CITY POS',
            style: pw.TextStyle(
              font: font,
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'HÓA ĐƠN BÁN HÀNG',
            style: pw.TextStyle(
              font: font,
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'SALES INVOICE',
            style: pw.TextStyle(font: font, fontSize: 14),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInvoiceInfo(Order order, pw.Font? font) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'Số hóa đơn: ${order.orderNumber ?? 'N/A'}',
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
            pw.SizedBox(height: 5),
            pw.Text(
              'Ngày: ${_formatDate(order.createdAt)}',
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
          ],
        ),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            pw.Text(
              'Trạng thái: ${order.status}',
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
            pw.SizedBox(height: 5),
            pw.Text(
              'Thanh toán: ${order.paymentMethod ?? 'Tiền mặt'}',
              style: pw.TextStyle(font: font, fontSize: 12),
            ),
          ],
        ),
      ],
    );
  }

  static pw.Widget _buildCustomerInfo(Order order, pw.Font? font) {
    String customerName = 'Khách lẻ';
    String customerPhone = '';

    // Extract customer info from notes if available
    if (order.notes != null && order.notes!.contains('Khách hàng:')) {
      final parts = order.notes!.split(' - ');
      if (parts.isNotEmpty) {
        customerName = parts[0].replaceAll('Khách hàng: ', '');
      }
      if (parts.length > 1) {
        customerPhone = parts[1].replaceAll('SĐT: ', '');
      }
    }

    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey),
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'THÔNG TIN KHÁCH HÀNG',
            style: pw.TextStyle(
              font: font,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'Tên: $customerName',
            style: pw.TextStyle(font: font, fontSize: 11),
          ),
          if (customerPhone.isNotEmpty) ...[
            pw.SizedBox(height: 3),
            pw.Text(
              'SĐT: $customerPhone',
              style: pw.TextStyle(font: font, fontSize: 11),
            ),
          ],
        ],
      ),
    );
  }

  static pw.Widget _buildItemsTable(Order order, pw.Font? font) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('STT', font, isHeader: true),
            _buildTableCell('Tên sản phẩm', font, isHeader: true),
            _buildTableCell('SL', font, isHeader: true),
            _buildTableCell('Đơn giá', font, isHeader: true),
            _buildTableCell('Thành tiền', font, isHeader: true),
          ],
        ),
        // Items
        ...order.items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          return pw.TableRow(
            children: [
              _buildTableCell('${index + 1}', font),
              _buildTableCell(item.productName ?? 'N/A', font),
              _buildTableCell('${item.quantity}', font),
              _buildTableCell(_formatCurrency(item.unitPrice), font),
              _buildTableCell(_formatCurrency(item.totalAmount), font),
            ],
          );
        }),
      ],
    );
  }

  static pw.Widget _buildTableCell(
    String text,
    pw.Font? font, {
    bool isHeader = false,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: isHeader ? 11 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static pw.Widget _buildTotal(Order order, pw.Font? font) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Container(
            width: 200,
            child: pw.Column(
              children: [
                _buildTotalRow(
                  'Tạm tính:',
                  _formatCurrency(order.subtotal),
                  font,
                ),
                if (order.discountAmount > 0)
                  _buildTotalRow(
                    'Giảm giá:',
                    '-${_formatCurrency(order.discountAmount)}',
                    font,
                  ),
                pw.Divider(color: PdfColors.grey),
                _buildTotalRow(
                  'TỔNG CỘNG:',
                  _formatCurrency(order.totalAmount),
                  font,
                  isTotal: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildTotalRow(
    String label,
    String value,
    pw.Font? font, {
    bool isTotal = false,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: font,
              fontSize: isTotal ? 12 : 11,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              font: font,
              fontSize: isTotal ? 12 : 11,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(pw.Font? font) {
    return pw.Container(
      width: double.infinity,
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            'Cảm ơn quý khách đã mua hàng!',
            style: pw.TextStyle(
              font: font,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'Thank you for your purchase!',
            style: pw.TextStyle(font: font, fontSize: 10),
          ),
        ],
      ),
    );
  }

  // Format date
  static String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  // Format currency
  static String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}₫';
  }
}
