import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

import 'product_image_service.dart';

/// Service to handle product image upload operations
/// Reusable across different screens (add product, edit product, etc.)
class ProductImageUploadService {
  static final ImagePicker _picker = ImagePicker();

  /// Pick image from gallery and upload
  static Future<String?> pickFromGalleryAndUpload({
    required String productId,
    String altText = 'Product image',
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image == null) return null;

      return await _uploadImage(
        productId: productId,
        imageFile: File(image.path),
        altText: altText,
        onProgress: onProgress,
        onError: onError,
      );
    } catch (e) {
      onError?.call('Lỗi khi chọn ảnh: $e');
      return null;
    }
  }

  /// Pick image from camera and upload
  static Future<String?> pickFromCameraAndUpload({
    required String productId,
    String altText = 'Product image',
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image == null) return null;

      return await _uploadImage(
        productId: productId,
        imageFile: File(image.path),
        altText: altText,
        onProgress: onProgress,
        onError: onError,
      );
    } catch (e) {
      onError?.call('Lỗi khi chụp ảnh: $e');
      return null;
    }
  }

  /// Upload image file directly
  static Future<String?> uploadImageFile({
    required String productId,
    required File imageFile,
    String altText = 'Product image',
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    return await _uploadImage(
      productId: productId,
      imageFile: imageFile,
      altText: altText,
      onProgress: onProgress,
      onError: onError,
    );
  }

  /// Internal method to handle the actual upload
  static Future<String?> _uploadImage({
    required String productId,
    required File imageFile,
    required String altText,
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    try {
      onProgress?.call('Đang tải ảnh lên...');

      // Use existing ProductImageService for upload
      final result = await ProductImageService.uploadProductImage(
        productId: productId,
        imageFile: imageFile,
        altText: altText,
      );

      if (result['success'] == true) {
        onProgress?.call('Tải ảnh thành công!');
        return result['imageUrl'] as String?;
      } else {
        onError?.call(result['error'] ?? 'Lỗi không xác định');
        return null;
      }
    } catch (e) {
      onError?.call('Lỗi khi tải ảnh: $e');
      return null;
    }
  }

  /// Show image picker dialog
  static void showImagePickerDialog({
    required BuildContext context,
    required String productId,
    String altText = 'Product image',
    Function(String)? onImageUploaded,
    Function(String)? onError,
  }) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Chọn từ thư viện'),
                onTap: () async {
                  Navigator.pop(context);
                  final imageUrl = await pickFromGalleryAndUpload(
                    productId: productId,
                    altText: altText,
                    onError: onError,
                  );
                  if (imageUrl != null) {
                    onImageUploaded?.call(imageUrl);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Chụp ảnh'),
                onTap: () async {
                  Navigator.pop(context);
                  final imageUrl = await pickFromCameraAndUpload(
                    productId: productId,
                    altText: altText,
                    onError: onError,
                  );
                  if (imageUrl != null) {
                    onImageUploaded?.call(imageUrl);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text('Hủy'),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Generate a valid temporary UUID for product creation
  static String generateTempProductId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final random = (timestamp.hashCode % 10000).toString().padLeft(4, '0');
    return '00000000-0000-4000-8000-${timestamp.substring(timestamp.length - 8)}$random';
  }

  /// Transfer image from temporary product ID to real product ID
  static Future<bool> transferImageToProduct({
    required String tempProductId,
    required String realProductId,
  }) async {
    try {
      // Get the image info from temp ID
      final tempImageInfo = await ProductImageService.getProductImage(tempProductId);
      
      if (tempImageInfo != null) {
        // Update the storage_files record to use the real product ID
        await ProductImageService.updateImageProductId(
          imageId: tempImageInfo['id'],
          newProductId: realProductId,
        );
        
        debugPrint('✅ Transferred image from temp ID $tempProductId to product ID $realProductId');
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('⚠️ Failed to transfer image: $e');
      return false;
    }
  }

  /// Validate image file
  static Future<bool> validateImageFile(File imageFile) async {
    try {
      // Check file size (max 5MB)
      final fileSize = await imageFile.length();
      if (fileSize > 5 * 1024 * 1024) {
        return false;
      }

      // Check file extension
      final extension = imageFile.path.toLowerCase().split('.').last;
      if (!['jpg', 'jpeg', 'png', 'webp'].contains(extension)) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get image file size in MB
  static Future<double> getImageFileSizeMB(File imageFile) async {
    try {
      final fileSize = await imageFile.length();
      return fileSize / (1024 * 1024);
    } catch (e) {
      return 0.0;
    }
  }

  /// Delete product image
  static Future<bool> deleteProductImage({
    required String productId,
    Function(String)? onError,
  }) async {
    try {
      await ProductImageService.deleteProductImage(productId);
      return true;
    } catch (e) {
      onError?.call('Lỗi khi xóa ảnh: $e');
      return false;
    }
  }
}
