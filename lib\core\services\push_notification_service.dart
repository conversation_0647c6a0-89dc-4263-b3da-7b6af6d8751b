import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';

class PushNotificationService {
  static final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  
  static bool _isInitialized = false;

  // Initialize push notification service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // Combined initialization settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
        macOS: initializationSettingsIOS,
      );

      // Initialize the plugin
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Request permissions
      await _requestPermissions();

      _isInitialized = true;
      debugPrint('✅ Push notification service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing push notifications: $e');
    }
  }

  // Request notification permissions
  static Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      // Android 13+ requires notification permission
      final status = await Permission.notification.request();
      return status.isGranted;
    } else if (Platform.isIOS) {
      // iOS permissions
      final bool? result = await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
      return result ?? false;
    }
    return true;
  }

  // Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('📱 Notification tapped: ${response.payload}');
    // TODO: Handle navigation based on payload
  }

  // Send local push notification
  static Future<void> sendPushNotification({
    required String title,
    required String message,
    String? payload,
    int? id,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'city_pos_channel',
        'City POS Notifications',
        channelDescription: 'Notifications for City POS app',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
        macOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin.show(
        id ?? DateTime.now().millisecondsSinceEpoch ~/ 1000,
        title,
        message,
        platformChannelSpecifics,
        payload: payload,
      );

      debugPrint('📱 Push notification sent: $title');
    } catch (e) {
      debugPrint('❌ Error sending push notification: $e');
    }
  }

  // Send notification for voucher creation
  static Future<void> sendVoucherNotification({
    required String voucherType,
    required String voucherNumber,
    required double amount,
    String? additionalInfo,
  }) async {
    final title = 'Phiếu $voucherType đã tạo thành công';
    final message = 'Phiếu $voucherNumber - ${amount.toStringAsFixed(0)}₫'
        '${additionalInfo != null ? '\n$additionalInfo' : ''}';

    await sendPushNotification(
      title: title,
      message: message,
      payload: 'voucher:$voucherType:$voucherNumber',
    );
  }

  // Send notification for order creation
  static Future<void> sendOrderNotification({
    required String orderNumber,
    required double totalAmount,
    String? customerName,
  }) async {
    final title = 'Đơn hàng mới đã tạo';
    final message = 'Đơn hàng $orderNumber - ${totalAmount.toStringAsFixed(0)}₫'
        '${customerName != null ? '\nKhách hàng: $customerName' : ''}';

    await sendPushNotification(
      title: title,
      message: message,
      payload: 'order:$orderNumber',
    );
  }

  // Send notification for stock transaction
  static Future<void> sendStockNotification({
    required String transactionType,
    required int totalQuantity,
    required double totalAmount,
  }) async {
    final title = 'Phiếu ${transactionType == 'import' ? 'nhập' : 'xuất'} kho thành công';
    final message = 'Đã xử lý $totalQuantity sản phẩm - ${totalAmount.toStringAsFixed(0)}₫';

    await sendPushNotification(
      title: title,
      message: message,
      payload: 'stock:$transactionType',
    );
  }

  // Send notification for payment
  static Future<void> sendPaymentNotification({
    required String orderNumber,
    required double amount,
    required String paymentMethod,
  }) async {
    final title = 'Thanh toán thành công';
    final message = 'Đơn hàng $orderNumber - ${amount.toStringAsFixed(0)}₫\nPhương thức: $paymentMethod';

    await sendPushNotification(
      title: title,
      message: message,
      payload: 'payment:$orderNumber',
    );
  }

  // Send notification for invoice
  static Future<void> sendInvoiceNotification({
    required String invoiceNumber,
    required double totalAmount,
  }) async {
    final title = 'Hóa đơn đã tạo thành công';
    final message = 'Hóa đơn $invoiceNumber - ${totalAmount.toStringAsFixed(0)}₫';

    await sendPushNotification(
      title: title,
      message: message,
      payload: 'invoice:$invoiceNumber',
    );
  }

  // Check if notifications are enabled
  static Future<bool> areNotificationsEnabled() async {
    if (Platform.isAndroid) {
      return await Permission.notification.isGranted;
    } else if (Platform.isIOS) {
      final bool? result = await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
      return result ?? false;
    }
    return true;
  }

  // Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  // Cancel specific notification
  static Future<void> cancelNotification(int id) async {
    await _flutterLocalNotificationsPlugin.cancel(id);
  }
}
