import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static const String _rememberLoginKey = 'remember_login';
  static const String _savedEmailKey = 'saved_email';
  static const String _savedPasswordKey = 'saved_password';
  static const String _autoLoginKey = 'auto_login';

  static SharedPreferences? _prefs;

  // Initialize SharedPreferences
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Ensure preferences are initialized
  static Future<SharedPreferences> get _preferences async {
    if (_prefs == null) {
      await init();
    }
    return _prefs!;
  }

  // Remember login settings
  static Future<void> setRememberLogin(bool remember) async {
    final prefs = await _preferences;
    await prefs.setBool(_rememberLoginKey, remember);
  }

  static Future<bool> getRememberLogin() async {
    final prefs = await _preferences;
    return prefs.getBool(_rememberLoginKey) ?? false;
  }

  // Save login credentials (only if remember login is enabled)
  static Future<void> saveLoginCredentials({
    required String email,
    required String password,
  }) async {
    final prefs = await _preferences;
    await prefs.setString(_savedEmailKey, email);
    await prefs.setString(_savedPasswordKey, password);
  }

  // Get saved login credentials
  static Future<Map<String, String?>> getSavedLoginCredentials() async {
    final prefs = await _preferences;
    return {
      'email': prefs.getString(_savedEmailKey),
      'password': prefs.getString(_savedPasswordKey),
    };
  }

  // Clear saved login credentials
  static Future<void> clearLoginCredentials() async {
    final prefs = await _preferences;
    await prefs.remove(_savedEmailKey);
    await prefs.remove(_savedPasswordKey);
  }

  // Auto login setting
  static Future<void> setAutoLogin(bool autoLogin) async {
    final prefs = await _preferences;
    await prefs.setBool(_autoLoginKey, autoLogin);
  }

  static Future<bool> getAutoLogin() async {
    final prefs = await _preferences;
    return prefs.getBool(_autoLoginKey) ?? false;
  }

  // Clear all login-related data
  static Future<void> clearAllLoginData() async {
    final prefs = await _preferences;
    await prefs.remove(_rememberLoginKey);
    await prefs.remove(_savedEmailKey);
    await prefs.remove(_savedPasswordKey);
    await prefs.remove(_autoLoginKey);
  }

  // Check if user has saved credentials
  static Future<bool> hasSavedCredentials() async {
    final credentials = await getSavedLoginCredentials();
    return credentials['email'] != null && 
           credentials['password'] != null &&
           credentials['email']!.isNotEmpty &&
           credentials['password']!.isNotEmpty;
  }
}
