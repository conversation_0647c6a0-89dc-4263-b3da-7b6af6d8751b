import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import 'package:supabase_flutter/supabase_flutter.dart';

import '../config/supabase_config.dart';

class SupabaseStorageService {
  static final SupabaseClient? _supabase = SupabaseConfig.client;

  // Upload file to Supabase Storage
  static Future<Map<String, dynamic>> uploadFile({
    required String bucketName,
    required String filePath,
    required File file,
    String? fileName,
    bool isPublic = true,
    Map<String, String>? metadata,
  }) async {
    if (_supabase == null) {
      throw Exception('Supabase client not initialized');
    }

    try {
      // Generate file name if not provided
      fileName ??= '${DateTime.now().millisecondsSinceEpoch}_${path.basename(file.path)}';
      
      // Get file info
      final fileBytes = await file.readAsBytes();
      final fileSize = fileBytes.length;
      final mimeType = _getMimeType(fileName);
      
      // Upload to Supabase Storage
      final uploadPath = '$filePath/$fileName';
      await _supabase!.storage
          .from(bucketName)
          .uploadBinary(uploadPath, fileBytes);

      // Get public URL if public
      String? publicUrl;
      if (isPublic) {
        publicUrl = _supabase!.storage
            .from(bucketName)
            .getPublicUrl(uploadPath);
      }

      return {
        'file_path': uploadPath,
        'file_name': fileName,
        'file_size': fileSize,
        'mime_type': mimeType,
        'public_url': publicUrl,
        'bucket_name': bucketName,
      };
    } catch (e) {
      debugPrint('❌ Error uploading file: $e');
      throw Exception('Failed to upload file: $e');
    }
  }

  // Upload image with optimization
  static Future<Map<String, dynamic>> uploadImage({
    required String bucketName,
    required String filePath,
    required File imageFile,
    String? fileName,
    bool isPublic = true,
    int? maxWidth,
    int? maxHeight,
    int quality = 85,
    Map<String, String>? metadata,
  }) async {
    try {
      // Optimize image before upload
      final optimizedFile = await _optimizeImage(
        imageFile,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        quality: quality,
      );

      // Upload optimized image
      final result = await uploadFile(
        bucketName: bucketName,
        filePath: filePath,
        file: optimizedFile,
        fileName: fileName,
        isPublic: isPublic,
        metadata: metadata,
      );

      // Get image dimensions
      final imageBytes = await optimizedFile.readAsBytes();
      final image = img.decodeImage(imageBytes);
      
      if (image != null) {
        result['width'] = image.width;
        result['height'] = image.height;
      }

      // Clean up temporary file
      if (optimizedFile.path != imageFile.path) {
        await optimizedFile.delete();
      }

      return result;
    } catch (e) {
      debugPrint('❌ Error uploading image: $e');
      throw Exception('Failed to upload image: $e');
    }
  }

  // Delete file from storage
  static Future<void> deleteFile({
    required String bucketName,
    required String filePath,
  }) async {
    if (_supabase == null) {
      throw Exception('Supabase client not initialized');
    }

    try {
      await _supabase!.storage
          .from(bucketName)
          .remove([filePath]);
    } catch (e) {
      debugPrint('❌ Error deleting file: $e');
      throw Exception('Failed to delete file: $e');
    }
  }

  // Get signed URL for private files
  static Future<String> getSignedUrl({
    required String bucketName,
    required String filePath,
    int expiresIn = 3600, // 1 hour default
  }) async {
    if (_supabase == null) {
      throw Exception('Supabase client not initialized');
    }

    try {
      return await _supabase!.storage
          .from(bucketName)
          .createSignedUrl(filePath, expiresIn);
    } catch (e) {
      debugPrint('❌ Error getting signed URL: $e');
      throw Exception('Failed to get signed URL: $e');
    }
  }

  // Get public URL
  static String getPublicUrl({
    required String bucketName,
    required String filePath,
  }) {
    if (_supabase == null) {
      throw Exception('Supabase client not initialized');
    }

    return _supabase!.storage
        .from(bucketName)
        .getPublicUrl(filePath);
  }

  // List files in a folder
  static Future<List<FileObject>> listFiles({
    required String bucketName,
    String? folderPath,
    int limit = 100,
    int offset = 0,
  }) async {
    if (_supabase == null) {
      throw Exception('Supabase client not initialized');
    }

    try {
      return await _supabase!.storage
          .from(bucketName)
          .list(
            path: folderPath,
            searchOptions: SearchOptions(
              limit: limit,
              offset: offset,
            ),
          );
    } catch (e) {
      debugPrint('❌ Error listing files: $e');
      throw Exception('Failed to list files: $e');
    }
  }

  // Check if file exists
  static Future<bool> fileExists({
    required String bucketName,
    required String filePath,
  }) async {
    try {
      final files = await listFiles(
        bucketName: bucketName,
        folderPath: path.dirname(filePath),
      );
      
      final fileName = path.basename(filePath);
      return files.any((file) => file.name == fileName);
    } catch (e) {
      return false;
    }
  }

  // Private helper methods
  static String _getMimeType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.webp':
        return 'image/webp';
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }

  static Future<File> _optimizeImage(
    File imageFile, {
    int? maxWidth,
    int? maxHeight,
    int quality = 85,
  }) async {
    try {
      final imageBytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(imageBytes);
      
      if (image == null) return imageFile;

      // Resize if needed
      if (maxWidth != null || maxHeight != null) {
        image = img.copyResize(
          image,
          width: maxWidth,
          height: maxHeight,
          maintainAspect: true,
        );
      }

      // Encode with quality
      final optimizedBytes = img.encodeJpg(image, quality: quality);
      
      // Create temporary file
      final tempDir = Directory.systemTemp;
      final tempFile = File('${tempDir.path}/optimized_${DateTime.now().millisecondsSinceEpoch}.jpg');
      await tempFile.writeAsBytes(optimizedBytes);
      
      return tempFile;
    } catch (e) {
      debugPrint('❌ Error optimizing image: $e');
      return imageFile; // Return original if optimization fails
    }
  }
}
