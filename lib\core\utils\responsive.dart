import 'package:flutter/material.dart';

class Responsive {
  final BuildContext context;

  const Responsive(this.context);

  // Breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1440;

  // Instance methods
  bool get isMobile => MediaQuery.of(context).size.width < mobileBreakpoint;
  bool get isTablet =>
      MediaQuery.of(context).size.width >= mobileBreakpoint &&
      MediaQuery.of(context).size.width < tabletBreakpoint;
  bool get isDesktop => MediaQuery.of(context).size.width >= tabletBreakpoint;
  bool get isLargeDesktop =>
      MediaQuery.of(context).size.width >= desktopBreakpoint;

  // Check device type (static methods)
  static bool isMobileStatic(BuildContext context) =>
      MediaQuery.of(context).size.width < mobileBreakpoint;

  static bool isTabletStatic(BuildContext context) =>
      MediaQuery.of(context).size.width >= mobileBreakpoint &&
      MediaQuery.of(context).size.width < tabletBreakpoint;

  static bool isDesktopStatic(BuildContext context) =>
      MediaQuery.of(context).size.width >= tabletBreakpoint;

  static bool isLargeDesktopStatic(BuildContext context) =>
      MediaQuery.of(context).size.width >= desktopBreakpoint;

  // Get responsive value
  static T responsive<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
  }) {
    if (isLargeDesktopStatic(context) && largeDesktop != null) {
      return largeDesktop;
    } else if (isDesktopStatic(context) && desktop != null) {
      return desktop;
    } else if (isTabletStatic(context) && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  // Get responsive padding - Dynamic based on screen width
  static EdgeInsets responsivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    // Dynamic padding calculation
    double padding;
    if (width < 400) {
      padding = 8.0; // Very small phones
    } else if (width < 500) {
      padding = 10.0; // Small phones
    } else if (width < 600) {
      padding = 12.0; // Mobile
    } else if (width < 768) {
      padding = 16.0; // Large mobile
    } else if (width < 1024) {
      padding = 20.0; // Tablet
    } else if (width < 1200) {
      padding = 24.0; // Small desktop
    } else if (width < 1440) {
      padding = 32.0; // Desktop
    } else if (width < 1920) {
      padding = 40.0; // Large desktop
    } else {
      padding = 48.0; // Ultra wide
    }

    return EdgeInsets.all(padding);
  }

  // Get responsive horizontal padding
  static EdgeInsets responsiveHorizontalPadding(BuildContext context) {
    return EdgeInsets.symmetric(
      horizontal: responsive(
        context,
        mobile: 16.0,
        tablet: 24.0,
        desktop: 32.0,
        largeDesktop: 48.0,
      ),
    );
  }

  // Get responsive vertical padding
  static EdgeInsets responsiveVerticalPadding(BuildContext context) {
    return EdgeInsets.symmetric(
      vertical: responsive(
        context,
        mobile: 8.0,
        tablet: 12.0,
        desktop: 16.0,
        largeDesktop: 24.0,
      ),
    );
  }

  // Get responsive margin - Dynamic based on screen width
  static EdgeInsets responsiveMargin(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    // Dynamic margin calculation - smaller than padding
    double margin;
    if (width < 400) {
      margin = 4.0; // Very small phones
    } else if (width < 500) {
      margin = 5.0; // Small phones
    } else if (width < 600) {
      margin = 6.0; // Mobile
    } else if (width < 768) {
      margin = 8.0; // Large mobile
    } else if (width < 1024) {
      margin = 10.0; // Tablet
    } else if (width < 1200) {
      margin = 12.0; // Small desktop
    } else if (width < 1440) {
      margin = 16.0; // Desktop
    } else if (width < 1920) {
      margin = 20.0; // Large desktop
    } else {
      margin = 24.0; // Ultra wide
    }

    return EdgeInsets.all(margin);
  }

  // Get responsive font size - Dynamic scaling based on screen width
  static double responsiveFontSize(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
    double? largeDesktop,
  }) {
    final width = MediaQuery.of(context).size.width;

    // Calculate scale factor based on screen width
    double scaleFactor;
    if (width < 350) {
      scaleFactor = 0.85; // Very small phones - smaller text
    } else if (width < 400) {
      scaleFactor = 0.9; // Small phones
    } else if (width < 500) {
      scaleFactor = 0.95; // Medium phones
    } else if (width < 600) {
      scaleFactor = 1.0; // Large phones (base)
    } else if (width < 768) {
      scaleFactor = 1.05; // Large mobile
    } else if (width < 1024) {
      scaleFactor = 1.1; // Tablet
    } else if (width < 1200) {
      scaleFactor = 1.15; // Small desktop
    } else if (width < 1440) {
      scaleFactor = 1.2; // Desktop
    } else if (width < 1920) {
      scaleFactor = 1.25; // Large desktop
    } else {
      scaleFactor = 1.3; // Ultra wide
    }

    // Apply scale factor to base mobile size
    return mobile * scaleFactor;
  }

  // Get responsive grid count - Dynamic for dashboard cards
  static int responsiveGridCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    // Calculate optimal columns for dashboard cards (min width: 150px for mobile)
    const double minCardWidth = 150.0; // Reduced for mobile 2-column layout
    const double spacing = 16.0;
    const double padding = 32.0;

    final availableWidth = width - padding;
    final maxColumns = (availableWidth / (minCardWidth + spacing)).floor();

    // Apply constraints - Always at least 2 columns on mobile
    if (width < 400) return 2; // Very small screens - 2 columns
    if (width < 768)
      return maxColumns.clamp(2, 2); // Mobile - exactly 2 columns
    if (width < 1024) return maxColumns.clamp(2, 3); // Tablet - max 3
    if (width < 1200) return maxColumns.clamp(3, 4); // Small desktop - max 4
    return maxColumns.clamp(4, 6); // Large desktop - max 6
  }

  // Get responsive grid count for products - More granular
  static int responsiveProductGridCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    // Calculate optimal columns based on minimum card width (120px)
    const double minCardWidth = 120.0;
    const double spacing = 8.0;
    const double padding = 24.0; // Total horizontal padding

    final availableWidth = width - padding;
    final maxColumns = (availableWidth / (minCardWidth + spacing)).floor();

    // Apply constraints based on screen size
    if (width < 350) return 1; // Very small phones
    if (width < 480) return 2; // Small phones
    if (width < 600) return maxColumns.clamp(2, 2); // Mobile - max 2
    if (width < 768) return maxColumns.clamp(2, 3); // Large mobile - max 3
    if (width < 1024) return maxColumns.clamp(3, 4); // Tablet - max 4
    if (width < 1200) return maxColumns.clamp(4, 5); // Small desktop - max 5
    if (width < 1440) return maxColumns.clamp(4, 6); // Desktop - max 6
    return maxColumns.clamp(5, 8); // Large desktop - max 8
  }

  // Get responsive card aspect ratio - Adaptive based on grid count
  static double responsiveCardAspectRatio(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final gridCount = responsiveProductGridCount(context);

    // Adjust aspect ratio based on grid density
    double baseRatio;
    if (width < 600) {
      // Mobile: taller cards for better readability
      baseRatio = gridCount == 1 ? 1.2 : 0.85;
    } else if (width < 1024) {
      // Tablet: balanced ratio
      baseRatio = 0.9;
    } else {
      // Desktop: wider cards
      baseRatio = gridCount <= 4 ? 1.0 : 0.95;
    }

    return baseRatio;
  }

  // Get responsive card width
  static double responsiveCardWidth(BuildContext context) {
    return responsive(
      context,
      mobile: double.infinity,
      tablet: 300.0,
      desktop: 350.0,
      largeDesktop: 400.0,
    );
  }

  // Get responsive sidebar width
  static double responsiveSidebarWidth(BuildContext context) {
    return responsive(
      context,
      mobile: 280.0,
      tablet: 300.0,
      desktop: 320.0,
      largeDesktop: 350.0,
    );
  }

  // Get responsive app bar height
  static double responsiveAppBarHeight(BuildContext context) {
    return responsive(
      context,
      mobile: kToolbarHeight,
      tablet: kToolbarHeight + 8,
      desktop: kToolbarHeight + 16,
      largeDesktop: kToolbarHeight + 24,
    );
  }

  // Get responsive icon size
  static double responsiveIconSize(BuildContext context) {
    return responsive(
      context,
      mobile: 24.0,
      tablet: 28.0,
      desktop: 32.0,
      largeDesktop: 36.0,
    );
  }

  // Get responsive spacing - Dynamic based on screen width
  static double responsiveSpacing(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    // Dynamic spacing calculation
    if (width < 400) return 4.0; // Very small phones
    if (width < 500) return 6.0; // Small phones
    if (width < 600) return 8.0; // Mobile
    if (width < 768) return 10.0; // Large mobile
    if (width < 1024) return 12.0; // Tablet
    if (width < 1200) return 14.0; // Small desktop
    if (width < 1440) return 16.0; // Desktop
    if (width < 1920) return 18.0; // Large desktop
    return 20.0; // Ultra wide
  }

  // Get responsive border radius - Dynamic based on screen width
  static double responsiveBorderRadius(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    // Dynamic border radius calculation
    if (width < 400) return 6.0; // Very small phones
    if (width < 500) return 7.0; // Small phones
    if (width < 600) return 8.0; // Mobile
    if (width < 768) return 9.0; // Large mobile
    if (width < 1024) return 10.0; // Tablet
    if (width < 1200) return 11.0; // Small desktop
    if (width < 1440) return 12.0; // Desktop
    if (width < 1920) return 14.0; // Large desktop
    return 16.0; // Ultra wide
  }

  // Get responsive elevation
  static double responsiveElevation(BuildContext context) {
    return responsive(
      context,
      mobile: 2.0,
      tablet: 3.0,
      desktop: 4.0,
      largeDesktop: 6.0,
    );
  }

  // Get responsive text scale factor
  static double responsiveTextScale(BuildContext context) {
    return responsive(
      context,
      mobile: 0.9,
      tablet: 1.0,
      desktop: 1.1,
      largeDesktop: 1.2,
    );
  }

  // Check if should show drawer
  static bool shouldShowDrawer(BuildContext context) {
    return isMobileStatic(context) || isTabletStatic(context);
  }

  // Check if should show sidebar
  static bool shouldShowSidebar(BuildContext context) {
    return isDesktopStatic(context);
  }

  // Get responsive layout
  static Widget responsiveLayout({
    required BuildContext context,
    required Widget mobile,
    Widget? tablet,
    Widget? desktop,
    Widget? largeDesktop,
  }) {
    return responsive(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile,
      desktop: desktop ?? tablet ?? mobile,
      largeDesktop: largeDesktop ?? desktop ?? tablet ?? mobile,
    );
  }

  // Common font size helpers
  static double responsiveBodyText(BuildContext context) {
    return responsiveFontSize(context, mobile: 14.0);
  }

  static double responsiveCaptionText(BuildContext context) {
    return responsiveFontSize(context, mobile: 12.0);
  }

  static double responsiveSmallText(BuildContext context) {
    return responsiveFontSize(context, mobile: 10.0);
  }

  static double responsiveTitleText(BuildContext context) {
    return responsiveFontSize(context, mobile: 16.0);
  }

  static double responsiveHeadingText(BuildContext context) {
    return responsiveFontSize(context, mobile: 18.0);
  }

  static double responsiveLargeText(BuildContext context) {
    return responsiveFontSize(context, mobile: 20.0);
  }

  // Get responsive text style
  static TextStyle responsiveTextStyle(
    BuildContext context,
    TextStyle baseStyle, {
    double? fontSize,
  }) {
    return baseStyle.copyWith(
      fontSize: fontSize != null
          ? responsiveFontSize(context, mobile: fontSize)
          : baseStyle.fontSize,
    );
  }

  // Debug helper - Get current responsive values
  static Map<String, dynamic> getResponsiveDebugInfo(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return {
      'screenWidth': width,
      'spacing': responsiveSpacing(context),
      'padding': responsivePadding(context).left,
      'margin': responsiveMargin(context).left,
      'borderRadius': responsiveBorderRadius(context),
      'productGridCount': responsiveProductGridCount(context),
      'dashboardGridCount': responsiveGridCount(context),
      'cardAspectRatio': responsiveCardAspectRatio(context),
      'iconSize': responsiveIconSize(context),
      'bodyTextSize': responsiveBodyText(context),
      'titleTextSize': responsiveTitleText(context),
      'fontScaleFactor': _getFontScaleFactor(context),
    };
  }

  // Private helper to get current font scale factor
  static double _getFontScaleFactor(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 350) return 0.85;
    if (width < 400) return 0.9;
    if (width < 500) return 0.95;
    if (width < 600) return 1.0;
    if (width < 768) return 1.05;
    if (width < 1024) return 1.1;
    if (width < 1200) return 1.15;
    if (width < 1440) return 1.2;
    if (width < 1920) return 1.25;
    return 1.3;
  }
}
