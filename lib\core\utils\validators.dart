import '../constants/app_constants.dart';

class Validators {
  // Email validator
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email không được để trống';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Email không hợp lệ';
    }
    
    return null;
  }

  // Password validator
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Mật khẩu không được để trống';
    }
    
    if (value.length < AppConstants.minPasswordLength) {
      return 'Mật khẩu phải có ít nhất ${AppConstants.minPasswordLength} ký tự';
    }
    
    return null;
  }

  // Confirm password validator
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return '<PERSON><PERSON>c nhận mật khẩu không được để trống';
    }
    
    if (value != password) {
      return 'Mật khẩu xác nhận không khớp';
    }
    
    return null;
  }

  // Required field validator
  static String? validateRequired(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'Trường này'} không được để trống';
    }
    return null;
  }

  // Name validator
  static String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Tên không được để trống';
    }
    
    if (value.trim().length > AppConstants.maxNameLength) {
      return 'Tên không được vượt quá ${AppConstants.maxNameLength} ký tự';
    }
    
    return null;
  }

  // Phone number validator
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Số điện thoại không được để trống';
    }
    
    // Remove all non-digit characters
    final cleanedValue = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanedValue.length < 10 || cleanedValue.length > 11) {
      return 'Số điện thoại không hợp lệ';
    }
    
    // Check if starts with valid prefixes for Vietnamese phone numbers
    if (!cleanedValue.startsWith(RegExp(r'^(0[3-9]|84[3-9])'))) {
      return 'Số điện thoại không hợp lệ';
    }
    
    return null;
  }

  // Price validator
  static String? validatePrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'Giá không được để trống';
    }
    
    final price = double.tryParse(value.replaceAll(',', ''));
    if (price == null) {
      return 'Giá không hợp lệ';
    }
    
    if (price < 0) {
      return 'Giá không được âm';
    }
    
    return null;
  }

  // Quantity validator
  static String? validateQuantity(String? value) {
    if (value == null || value.isEmpty) {
      return 'Số lượng không được để trống';
    }
    
    final quantity = int.tryParse(value);
    if (quantity == null) {
      return 'Số lượng không hợp lệ';
    }
    
    if (quantity < 0) {
      return 'Số lượng không được âm';
    }
    
    return null;
  }

  // Product code validator
  static String? validateProductCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Mã sản phẩm không được để trống';
    }
    
    final code = value.trim().toUpperCase();
    if (code.length < 3 || code.length > 20) {
      return 'Mã sản phẩm phải từ 3-20 ký tự';
    }
    
    // Only allow alphanumeric characters and hyphens
    if (!RegExp(r'^[A-Z0-9-]+$').hasMatch(code)) {
      return 'Mã sản phẩm chỉ được chứa chữ, số và dấu gạch ngang';
    }
    
    return null;
  }

  // Discount validator
  static String? validateDiscount(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Discount is optional
    }
    
    final discount = double.tryParse(value);
    if (discount == null) {
      return 'Giảm giá không hợp lệ';
    }
    
    if (discount < 0 || discount > 100) {
      return 'Giảm giá phải từ 0-100%';
    }
    
    return null;
  }

  // Description validator
  static String? validateDescription(String? value) {
    if (value != null && value.length > AppConstants.maxDescriptionLength) {
      return 'Mô tả không được vượt quá ${AppConstants.maxDescriptionLength} ký tự';
    }
    return null;
  }

  // Tax ID validator (for Vietnamese businesses)
  static String? validateTaxId(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Tax ID is optional for some partners
    }
    
    final cleanedValue = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanedValue.length != 10 && cleanedValue.length != 13) {
      return 'Mã số thuế không hợp lệ';
    }
    
    return null;
  }

  // Bank account validator
  static String? validateBankAccount(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Bank account is optional
    }
    
    final cleanedValue = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanedValue.length < 8 || cleanedValue.length > 20) {
      return 'Số tài khoản không hợp lệ';
    }
    
    return null;
  }
}
