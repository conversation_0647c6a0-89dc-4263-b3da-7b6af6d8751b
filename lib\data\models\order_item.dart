import 'product.dart';

class OrderItem {
  final String? id;
  final String? orderId;
  final String? productId;
  final String? productName;
  final Product? product;
  final int quantity;
  final double unitPrice;
  final double discountAmount;
  final double totalAmount;
  final double totalPrice;
  final DateTime? createdAt;

  const OrderItem({
    this.id,
    this.orderId,
    this.productId,
    this.productName,
    this.product,
    this.quantity = 1,
    required this.unitPrice,
    this.discountAmount = 0.0,
    required this.totalAmount,
    required this.totalPrice,
    this.createdAt,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'] as String?,
      orderId: json['order_id'] as String?,
      productId: json['product_id'] as String?,
      product: json['product'] != null
          ? Product.fromJson(json['product'])
          : null,
      quantity: json['quantity'] as int? ?? 1,
      unitPrice: (json['unit_price'] as num).toDouble(),
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['total_amount'] as num).toDouble(),
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      totalPrice: (json['total_amount'] as num).toDouble(),
      productName: json['product_name'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (orderId != null) 'order_id': orderId,
      if (productId != null) 'product_id': productId,
      'quantity': quantity,
      'unit_price': unitPrice,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'total_price': totalPrice,
      // 'product_name': productName, // Commented out - column doesn't exist in DB
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
    };
  }

  OrderItem copyWith({
    String? id,
    String? orderId,
    String? productId,
    Product? product,
    int? quantity,
    double? unitPrice,
    double? discountAmount,
    double? totalAmount,
    double? totalPrice,
    String? productName,
    DateTime? createdAt,
  }) {
    return OrderItem(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      totalPrice: totalPrice ?? this.totalPrice,
      productName: productName ?? this.productName,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Factory constructor to create OrderItem from Product
  factory OrderItem.fromProduct(
    Product product, {
    int quantity = 1,
    double? customPrice,
    double discountAmount = 0.0,
  }) {
    final unitPrice = customPrice ?? product.price;
    final totalAmount = (unitPrice * quantity) - discountAmount;

    return OrderItem(
      productId: product.id,
      product: product,
      quantity: quantity,
      unitPrice: unitPrice,
      discountAmount: discountAmount,
      totalAmount: totalAmount,
      totalPrice: totalAmount,
      productName: product.name,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OrderItem(id: $id, productId: $productId, quantity: $quantity, total: $totalAmount)';
  }
}

// Extension for OrderItem
extension OrderItemExtension on OrderItem {
  // Get product name
  String get productName => product?.name ?? 'Unknown Product';

  // Get product SKU
  String get productSku => product?.sku ?? '';

  // Get formatted unit price
  String get formattedUnitPrice => '${unitPrice.toStringAsFixed(0)}đ';

  // Get formatted total
  String get formattedTotal => '${totalAmount.toStringAsFixed(0)}đ';

  // Get formatted discount
  String get formattedDiscount => '${discountAmount.toStringAsFixed(0)}đ';

  // Calculate subtotal (before discount)
  double get subtotal => unitPrice * quantity;

  // Get formatted subtotal
  String get formattedSubtotal => '${subtotal.toStringAsFixed(0)}đ';

  // Calculate discount percentage
  double get discountPercentage {
    if (subtotal == 0) return 0;
    return (discountAmount / subtotal) * 100;
  }

  // Get formatted discount percentage
  String get formattedDiscountPercentage =>
      '${discountPercentage.toStringAsFixed(1)}%';

  // Check if item has discount
  bool get hasDiscount => discountAmount > 0;

  // Get product unit
  String get productUnit => product?.unit ?? 'pcs';

  // Get display quantity with unit
  String get displayQuantity => '$quantity $productUnit';

  // Check if item is valid
  bool get isValid => quantity > 0 && unitPrice >= 0 && totalAmount >= 0;

  // Create copy with new quantity
  OrderItem copyWithQuantity(int newQuantity) {
    final newTotal = (unitPrice * newQuantity) - discountAmount;
    return copyWith(quantity: newQuantity, totalAmount: newTotal);
  }

  // Create copy with new unit price
  OrderItem copyWithUnitPrice(double newUnitPrice) {
    final newTotal = (newUnitPrice * quantity) - discountAmount;
    return copyWith(unitPrice: newUnitPrice, totalAmount: newTotal);
  }

  // Create copy with new discount
  OrderItem copyWithDiscount(double newDiscountAmount) {
    final newTotal = (unitPrice * quantity) - newDiscountAmount;
    return copyWith(discountAmount: newDiscountAmount, totalAmount: newTotal);
  }

  // Create copy with calculated total
  OrderItem copyWithCalculatedTotal() {
    final newTotal = (unitPrice * quantity) - discountAmount;
    return copyWith(totalAmount: newTotal);
  }
}
