import '../../core/config/supabase_config.dart';
import '../models/category.dart';

class CategoryService {
  static final _supabase = SupabaseConfig.client;

  // Lấy danh sách categories
  static Future<List<Category>> getCategories() async {
    try {
      final response = await _supabase
          .from('categories')
          .select()
          .eq('is_active', true)
          .order('name');

      return response.map<Category>((data) => Category.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Không thể tải danh sách danh mục: $e');
    }
  }

  // Lấy category theo ID
  static Future<Category?> getCategoryById(String id) async {
    try {
      final response = await _supabase
          .from('categories')
          .select()
          .eq('id', id)
          .single();

      return Category.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Tạo category mới
  static Future<Category> createCategory(Category category) async {
    try {
      // Thêm thông tin người tạo và thời gian
      final data = {
        ...category.toJson(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('categories')
          .insert(data)
          .select()
          .single();

      return Category.fromJson(response);
    } catch (e) {
      throw Exception('Không thể tạo danh mục mới: $e');
    }
  }

  // Cập nhật category
  static Future<bool> updateCategory(String id, Category category) async {
    try {
      // Thêm thông tin thời gian cập nhật
      final data = {
        ...category.toJson(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase.from('categories').update(data).eq('id', id);

      return true;
    } catch (e) {
      throw Exception('Không thể cập nhật danh mục: $e');
    }
  }

  // Xóa category (soft delete)
  static Future<bool> deleteCategory(String id) async {
    try {
      await _supabase
          .from('categories')
          .update({
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', id);

      return true;
    } catch (e) {
      throw Exception('Không thể xóa danh mục: $e');
    }
  }

  // Tìm kiếm categories
  static Future<List<Category>> searchCategories(String query) async {
    try {
      final response = await _supabase
          .from('categories')
          .select()
          .or('name.ilike.%$query%,description.ilike.%$query%')
          .eq('is_active', true)
          .order('name');

      return response.map<Category>((data) => Category.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Không thể tìm kiếm danh mục: $e');
    }
  }

  // Kiểm tra xem category có đang được sử dụng không
  static Future<bool> isCategoryInUse(String categoryId) async {
    try {
      final response = await _supabase
          .from('products')
          .select('id')
          .eq('category_id', categoryId)
          .limit(1);

      return response.isNotEmpty;
    } catch (e) {
      throw Exception('Không thể kiểm tra danh mục đang sử dụng: $e');
    }
  }
}
