import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/config/supabase_config.dart';

class DashboardService {
  static SupabaseClient? get _supabase => SupabaseConfig.client;

  // Get today's orders count
  static Future<int> getTodayOrdersCount() async {
    if (_supabase == null) {
      print('🔥 Supabase not initialized for orders count');
      return 0;
    }

    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

      print('🔥 Getting today orders count...');
      print('🔥 Date range: $startOfDay to $endOfDay');

      final response = await _supabase!
          .from('orders')
          .select('id')
          .gte('created_at', startOfDay.toIso8601String())
          .lte('created_at', endOfDay.toIso8601String());

      print('🔥 Today orders count: ${response.length}');
      return response.length;
    } catch (e) {
      print('🔥 Error getting today orders count: $e');
      return 0;
    }
  }

  // Get today's revenue
  static Future<double> getTodayRevenue() async {
    if (_supabase == null) {
      print('🔥 Supabase not initialized for revenue');
      return 0.0;
    }

    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

      print('🔥 Getting today revenue...');
      print('🔥 Date range: $startOfDay to $endOfDay');

      final response = await _supabase!
          .from('orders')
          .select('total_amount, status')
          .gte('created_at', startOfDay.toIso8601String())
          .lte('created_at', endOfDay.toIso8601String())
          .eq('status', 'completed');

      double totalRevenue = 0.0;
      for (final order in response) {
        final amount = (order['total_amount'] as num?)?.toDouble() ?? 0.0;
        totalRevenue += amount;
      }

      print(
        '🔥 Today revenue: $totalRevenue from ${response.length} completed orders',
      );
      return totalRevenue;
    } catch (e) {
      print('🔥 Error getting today revenue: $e');
      return 0.0;
    }
  }

  // Get low stock products count
  static Future<int> getLowStockProductsCount() async {
    if (_supabase == null) {
      print('🔥 Supabase not initialized for low stock');
      return 0;
    }

    try {
      print('🔥 Getting low stock products count...');

      // Get all active products with stock information
      final response = await _supabase!
          .from('products')
          .select('id, name, sku, stock_quantity, min_stock_level, is_active')
          .eq('is_active', true);

      print('🔥 Total active products: ${response.length}');

      int lowStockCount = 0;
      List<String> lowStockProducts = [];

      for (final product in response) {
        final stockQty = (product['stock_quantity'] as num?)?.toInt() ?? 0;
        final minLevel = (product['min_stock_level'] as num?)?.toInt() ?? 0;

        // Product is considered low stock if current stock <= minimum level
        if (stockQty <= minLevel) {
          lowStockCount++;
          lowStockProducts.add(
            '${product['name']} (${product['sku'] ?? 'N/A'})',
          );
          print(
            '🔥 Low stock: ${product['name']} - Stock: $stockQty, Min: $minLevel',
          );
        }
      }

      print(
        '🔥 Low stock products count: $lowStockCount out of ${response.length} products',
      );
      if (lowStockProducts.isNotEmpty) {
        print(
          '🔥 Low stock products: ${lowStockProducts.take(5).join(', ')}${lowStockProducts.length > 5 ? '...' : ''}',
        );
      }

      return lowStockCount;
    } catch (e) {
      print('🔥 Error getting low stock products count: $e');
      return 0;
    }
  }

  // Get detailed low stock products information
  static Future<List<Map<String, dynamic>>> getLowStockProductsDetails() async {
    if (_supabase == null) {
      print('🔥 Supabase not initialized for low stock details');
      return [];
    }

    try {
      print('🔥 Getting low stock products details...');

      // Get all active products with complete information
      final response = await _supabase!
          .from('products')
          .select(
            'id, name, sku, stock_quantity, min_stock_level, unit, price, is_active',
          )
          .eq('is_active', true);

      List<Map<String, dynamic>> lowStockProducts = [];

      for (final product in response) {
        final stockQty = (product['stock_quantity'] as num?)?.toInt() ?? 0;
        final minLevel = (product['min_stock_level'] as num?)?.toInt() ?? 0;

        // Product is considered low stock if current stock <= minimum level
        if (stockQty <= minLevel) {
          lowStockProducts.add({
            'id': product['id'],
            'name': product['name'],
            'sku': product['sku'],
            'currentStock': stockQty,
            'minLevel': minLevel,
            'unit': product['unit'] ?? 'pcs',
            'price': (product['price'] as num?)?.toDouble() ?? 0.0,
            'stockStatus': stockQty == 0 ? 'out_of_stock' : 'low_stock',
            'shortage': minLevel - stockQty,
          });
        }
      }

      // Sort by most critical (lowest stock first)
      lowStockProducts.sort(
        (a, b) => a['currentStock'].compareTo(b['currentStock']),
      );

      print(
        '🔥 Low stock products details: ${lowStockProducts.length} products',
      );
      for (final product in lowStockProducts.take(3)) {
        print(
          '🔥 - ${product['name']}: ${product['currentStock']}/${product['minLevel']} ${product['unit']}',
        );
      }

      return lowStockProducts;
    } catch (e) {
      print('🔥 Error getting low stock products details: $e');
      return [];
    }
  }

  // Get total customers count
  static Future<int> getTotalCustomersCount() async {
    if (_supabase == null) {
      print('🔥 Supabase not initialized for customers');
      return 0;
    }

    try {
      print('🔥 Getting total customers count...');

      // First, let's check what columns exist in partners table
      final allPartnersResponse = await _supabase!
          .from('partners')
          .select('*')
          .limit(1);

      if (allPartnersResponse.isNotEmpty) {
        print(
          '🔥 Partners table columns: ${allPartnersResponse.first.keys.toList()}',
        );
      }

      // Try different approaches to get customers
      List<dynamic> customersResponse = [];

      // Approach 1: Try with 'customer' type
      try {
        customersResponse = await _supabase!
            .from('partners')
            .select('id, name, type')
            .eq('type', 'customer')
            .eq('is_active', true);
        print(
          '🔥 Approach 1 (type=customer, is_active=true): ${customersResponse.length} customers',
        );
      } catch (e) {
        print('🔥 Approach 1 failed: $e');
      }

      // Approach 2: If no results, try without is_active filter
      if (customersResponse.isEmpty) {
        try {
          customersResponse = await _supabase!
              .from('partners')
              .select('id, name, type')
              .eq('type', 'customer');
          print(
            '🔥 Approach 2 (type=customer only): ${customersResponse.length} customers',
          );
        } catch (e) {
          print('🔥 Approach 2 failed: $e');
        }
      }

      // Approach 3: If still no results, get all partners and filter manually
      if (customersResponse.isEmpty) {
        try {
          final allPartners = await _supabase!
              .from('partners')
              .select('id, name, type, is_active');

          print('🔥 Total partners in database: ${allPartners.length}');

          // Debug: show all partner types
          final typeCounts = <String, int>{};
          for (final partner in allPartners) {
            final type = partner['type']?.toString() ?? 'null';
            typeCounts[type] = (typeCounts[type] ?? 0) + 1;
          }
          print('🔥 Partner types distribution: $typeCounts');

          // Filter customers manually
          customersResponse = allPartners.where((partner) {
            final type = partner['type']?.toString();
            final isActive = partner['is_active'] as bool? ?? true;
            return type == 'customer' && isActive;
          }).toList();

          print(
            '🔥 Approach 3 (manual filter): ${customersResponse.length} customers',
          );
        } catch (e) {
          print('🔥 Approach 3 failed: $e');
        }
      }

      // Show sample customers for debugging
      if (customersResponse.isNotEmpty) {
        print('🔥 Sample customers:');
        for (final customer in customersResponse.take(3)) {
          print('🔥 - ${customer['name']} (type: ${customer['type']})');
        }
        if (customersResponse.length > 3) {
          print('🔥 - ... and ${customersResponse.length - 3} more customers');
        }
      } else {
        print('🔥 No customers found in database');
        print('🔥 This might indicate:');
        print('🔥 1. No customers exist in the database');
        print('🔥 2. Column names are different (type vs partner_type)');
        print('🔥 3. Values are different (customer vs Customer)');
        print('🔥 4. Database connection issues');
      }

      return customersResponse.length;
    } catch (e) {
      print('🔥 Error getting total customers count: $e');
      return 0;
    }
  }

  // Get partners statistics
  static Future<Map<String, int>> getPartnersStats() async {
    if (_supabase == null) {
      print('🔥 Supabase not initialized for partners stats');
      return {
        'totalCustomers': 0,
        'totalSuppliers': 0,
        'totalPartners': 0,
        'activeCustomers': 0,
        'activeSuppliers': 0,
        'activePartners': 0,
      };
    }

    try {
      print('🔥 Getting partners statistics...');

      // Get all partners data
      final response = await _supabase!
          .from('partners')
          .select('id, name, type, is_active');

      int totalCustomers = 0;
      int totalSuppliers = 0;
      int totalBoth = 0;
      int activeCustomers = 0;
      int activeSuppliers = 0;
      int activeBoth = 0;

      for (final partner in response) {
        final type = partner['type'] as String;
        final isActive = partner['is_active'] as bool? ?? false;

        switch (type) {
          case 'customer':
            totalCustomers++;
            if (isActive) activeCustomers++;
            break;
          case 'supplier':
            totalSuppliers++;
            if (isActive) activeSuppliers++;
            break;
          case 'both':
            totalBoth++;
            if (isActive) activeBoth++;
            break;
        }
      }

      final stats = {
        'totalCustomers': totalCustomers,
        'totalSuppliers': totalSuppliers,
        'totalBoth': totalBoth,
        'totalPartners': response.length,
        'activeCustomers': activeCustomers,
        'activeSuppliers': activeSuppliers,
        'activeBoth': activeBoth,
        'activePartners': activeCustomers + activeSuppliers + activeBoth,
      };

      print('🔥 Partners stats: $stats');
      return stats;
    } catch (e) {
      print('🔥 Error getting partners stats: $e');
      return {
        'totalCustomers': 0,
        'totalSuppliers': 0,
        'totalPartners': 0,
        'activeCustomers': 0,
        'activeSuppliers': 0,
        'activePartners': 0,
      };
    }
  }

  // Get all dashboard data at once
  static Future<Map<String, dynamic>> getDashboardData() async {
    print('🔥 Loading dashboard data...');

    try {
      // Load all data in parallel for better performance
      final results = await Future.wait([
        getTodayRevenue(),
        getTodayOrdersCount(),
        getLowStockProductsCount(),
        getTotalCustomersCount(),
      ]);

      final dashboardData = {
        'todayRevenue': results[0] as double,
        'todayOrders': results[1] as int,
        'lowStockCount': results[2] as int,
        'totalCustomers': results[3] as int,
      };

      print('🔥 Dashboard data loaded successfully: $dashboardData');
      return dashboardData;
    } catch (e) {
      print('🔥 Error loading dashboard data: $e');
      return {
        'todayRevenue': 0.0,
        'todayOrders': 0,
        'lowStockCount': 0,
        'totalCustomers': 0,
      };
    }
  }

  // Get database statistics for debugging
  static Future<Map<String, dynamic>> getDatabaseStats() async {
    if (_supabase == null) {
      return {
        'totalOrders': 0,
        'totalProducts': 0,
        'totalPartners': 0,
        'error': 'Supabase not initialized',
      };
    }

    try {
      print('🔥 Getting database statistics...');

      final results = await Future.wait([
        _supabase!.from('orders').select('id'),
        _supabase!.from('products').select('id').eq('is_active', true),
        _supabase!.from('partners').select('id').eq('is_active', true),
      ]);

      final stats = {
        'totalOrders': results[0].length,
        'totalProducts': results[1].length,
        'totalPartners': results[2].length,
      };

      print('🔥 Database stats: $stats');
      return stats;
    } catch (e) {
      print('🔥 Error getting database stats: $e');
      return {
        'totalOrders': 0,
        'totalProducts': 0,
        'totalPartners': 0,
        'error': e.toString(),
      };
    }
  }
}
