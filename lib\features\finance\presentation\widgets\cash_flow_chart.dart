import 'package:flutter/material.dart';

import '../../../../core/widgets/app_card.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../domain/entities/transaction.dart';

class Cash<PERSON>low<PERSON>hart extends StatelessWidget {
  final List<Transaction> transactions;

  const CashFlowChart({super.key, required this.transactions});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Biểu đồ dòng tiền',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildChart(context),
        ],
      ),
    );
  }

  Widget _buildChart(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    if (transactions.isEmpty) {
      return Container(
        height: 200,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 48,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              l10n.noChartData,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              l10n.addTransactionForChart,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.withOpacity(0.6),
              ),
            ),
          ],
        ),
      );
    }

    // Simple bar chart representation
    return SizedBox(height: 200, child: _buildSimpleChart(context));
  }

  Widget _buildSimpleChart(BuildContext context) {
    final incomeTransactions = transactions
        .where((t) => t.type == 'income')
        .toList();
    final expenseTransactions = transactions
        .where((t) => t.type == 'expense')
        .toList();

    final totalIncome = incomeTransactions.fold<double>(
      0,
      (sum, t) => sum + t.amount,
    );
    final totalExpense = expenseTransactions.fold<double>(
      0,
      (sum, t) => sum + t.amount,
    );

    final maxAmount = [
      totalIncome,
      totalExpense,
    ].reduce((a, b) => a > b ? a : b);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        _buildBar(context, 'Thu', totalIncome, maxAmount, Colors.green),
        _buildBar(context, 'Chi', totalExpense, maxAmount, Colors.red),
      ],
    );
  }

  Widget _buildBar(
    BuildContext context,
    String label,
    double amount,
    double maxAmount,
    Color color,
  ) {
    final height = maxAmount > 0 ? (amount / maxAmount) * 150 : 0.0;

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          '${(amount / 1000000).toStringAsFixed(1)}M',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Container(
          width: 60,
          height: height,
          decoration: BoxDecoration(
            color: color,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }
}
