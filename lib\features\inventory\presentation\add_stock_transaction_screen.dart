import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/services/product_service.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../data/services/stock_transaction_service.dart';
import '../domain/entities/stock_transaction.dart';

class AddStockTransactionScreen extends ConsumerStatefulWidget {
  const AddStockTransactionScreen({super.key});

  @override
  ConsumerState<AddStockTransactionScreen> createState() =>
      _AddStockTransactionScreenState();
}

class _AddStockTransactionScreenState
    extends ConsumerState<AddStockTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _productController = TextEditingController();
  final _quantityController = TextEditingController();
  final _priceController = TextEditingController();
  final _noteController = TextEditingController();
  final _referenceController = TextEditingController();

  String _transactionType = 'in'; // 'in' or 'out'
  Map<String, dynamic>? _selectedProduct;
  bool _isLoading = false;
  bool _isLoadingProducts = false;
  List<Map<String, dynamic>> _products = [];

  final List<String> _transactionTypes = ['in', 'out'];

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  // Load products from Supabase
  Future<void> _loadProducts() async {
    setState(() {
      _isLoadingProducts = true;
    });

    try {
      final products = await ProductService.getProducts(
        isActive: true,
        limit: 100,
      );

      // Convert Product objects to Map for compatibility
      final productMaps = products
          .map(
            (product) => {
              'id': product.id,
              'name': product.name,
              'sku': product.sku,
              'barcode': product.barcode,
              'price': product.price,
              'cost': product.cost,
              'stock_quantity': product.stockQuantity,
              'unit': product.unit,
            },
          )
          .toList();

      setState(() {
        _products = productMaps;
        _isLoadingProducts = false;
      });
    } catch (e) {
      print('Error loading products: $e');
      setState(() {
        _isLoadingProducts = false;
      });
    }
  }

  @override
  void dispose() {
    _productController.dispose();
    _quantityController.dispose();
    _priceController.dispose();
    _noteController.dispose();
    _referenceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.addStockTransactionScreenTitle),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.stockTransactions),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveTransaction,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(l10n.saveButton),
          ),
        ],
      ),
      body: responsive.isMobile
          ? _buildMobileLayout(l10n)
          : _buildDesktopLayout(l10n),
    );
  }

  Widget _buildMobileLayout(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTransactionTypeSection(l10n),
            const SizedBox(height: 24),
            _buildProductSection(l10n),
            const SizedBox(height: 24),
            _buildQuantityPriceSection(l10n),
            const SizedBox(height: 24),
            _buildAdditionalInfoSection(l10n),
            const SizedBox(height: 32),
            _buildActionButtons(l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(AppLocalizations l10n) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Column(
                        children: [
                          _buildTransactionTypeSection(l10n),
                          const SizedBox(height: 24),
                          _buildProductSection(l10n),
                        ],
                      ),
                    ),
                    const SizedBox(width: 24),
                    Expanded(
                      child: Column(
                        children: [
                          _buildQuantityPriceSection(l10n),
                          const SizedBox(height: 24),
                          _buildAdditionalInfoSection(l10n),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 32),
                _buildActionButtons(l10n),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionTypeSection(AppLocalizations l10n) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Loại giao dịch',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _transactionType,
              decoration: const InputDecoration(
                labelText: 'Loại giao dịch *',
                border: OutlineInputBorder(),
              ),
              items: _transactionTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Row(
                    children: [
                      Icon(
                        type == 'in' ? Icons.add_circle : Icons.remove_circle,
                        color: type == 'in' ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(_getTransactionTypeLabel(type, l10n)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _transactionType = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductSection(AppLocalizations l10n) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.productSectionTitle,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Product search field with autocomplete
            Autocomplete<Map<String, dynamic>>(
              optionsBuilder: (TextEditingValue textEditingValue) {
                if (textEditingValue.text.isEmpty) {
                  return _products.take(10);
                }

                final filtered = _products.where((product) {
                  final name = product['name']?.toString().toLowerCase() ?? '';
                  final sku = product['sku']?.toString().toLowerCase() ?? '';
                  final barcode =
                      product['barcode']?.toString().toLowerCase() ?? '';
                  final searchQuery = textEditingValue.text.toLowerCase();

                  return name.contains(searchQuery) ||
                      sku.contains(searchQuery) ||
                      barcode.contains(searchQuery);
                }).toList();

                return filtered.take(10);
              },
              displayStringForOption: (Map<String, dynamic> option) {
                final name = option['name']?.toString() ?? 'Unknown';
                final sku = option['sku']?.toString() ?? '';
                return sku.isNotEmpty ? '$name ($sku)' : name;
              },
              fieldViewBuilder:
                  (context, controller, focusNode, onEditingComplete) {
                    _productController.text = controller.text;
                    return TextFormField(
                      controller: controller,
                      focusNode: focusNode,
                      onEditingComplete: onEditingComplete,
                      decoration: InputDecoration(
                        labelText: l10n.selectProductLabel,
                        hintText: 'Tìm kiếm theo tên, SKU hoặc barcode...',
                        border: const OutlineInputBorder(),
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _isLoadingProducts
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : null,
                      ),
                      validator: (value) {
                        if (_selectedProduct == null) {
                          return l10n.pleaseSelectProduct;
                        }
                        return null;
                      },
                    );
                  },
              optionsViewBuilder: (context, onSelected, options) {
                return Align(
                  alignment: Alignment.topLeft,
                  child: Material(
                    elevation: 4.0,
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        maxHeight: 200,
                        maxWidth: 300,
                      ),
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        itemCount: options.length,
                        itemBuilder: (context, index) {
                          final product = options.elementAt(index);
                          final name = product['name']?.toString() ?? 'Unknown';
                          final sku = product['sku']?.toString() ?? '';
                          final stock =
                              product['stock_quantity']?.toString() ?? '0';

                          return ListTile(
                            dense: true,
                            title: Text(name),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (sku.isNotEmpty) Text('SKU: $sku'),
                                Text(
                                  'Tồn kho: $stock',
                                  style: TextStyle(
                                    color: int.tryParse(stock) == 0
                                        ? Colors.red
                                        : Colors.green,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            onTap: () => onSelected(product),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
              onSelected: (Map<String, dynamic> selection) {
                setState(() {
                  _selectedProduct = selection;
                  // Auto-fill price if available
                  if (selection['price'] != null) {
                    _priceController.text = selection['price'].toString();
                  }
                });
              },
            ),

            // Show selected product info
            if (_selectedProduct != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.green.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedProduct!['name']?.toString() ?? 'Unknown',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            'Tồn kho: ${_selectedProduct!['stock_quantity'] ?? 0}',
                            style: TextStyle(
                              color:
                                  (_selectedProduct!['stock_quantity'] ?? 0) ==
                                      0
                                  ? Colors.red
                                  : Colors.green,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.clear, size: 20),
                      onPressed: () {
                        setState(() {
                          _selectedProduct = null;
                          _productController.clear();
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuantityPriceSection(AppLocalizations l10n) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.quantityAndPriceTitle,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _quantityController,
              decoration: InputDecoration(
                labelText: l10n.quantityLabel,
                border: const OutlineInputBorder(),
                suffixText: l10n.unitLabel,
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return l10n.pleaseEnterQuantity;
                }
                final quantity = int.tryParse(value);
                if (quantity == null || quantity <= 0) {
                  return l10n.invalidQuantity;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _priceController,
              decoration: InputDecoration(
                labelText: l10n.unitPriceLabel,
                border: const OutlineInputBorder(),
                suffixText: '₫',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final price = double.tryParse(value);
                  if (price == null || price < 0) {
                    return l10n.invalidUnitPrice;
                  }
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection(AppLocalizations l10n) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.additionalInfoTitle,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _referenceController,
              decoration: InputDecoration(
                labelText: l10n.referenceNumberLabel,
                border: const OutlineInputBorder(),
                hintText: l10n.referenceNumberHint,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _noteController,
              decoration: InputDecoration(
                labelText: l10n.noteLabel,
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading
                ? null
                : () => context.go(AppRoutes.stockTransactions),
            child: Text(l10n.cancelButton),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveTransaction,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(l10n.saveTransactionButton),
          ),
        ),
      ],
    );
  }

  String _getTransactionTypeLabel(String type, AppLocalizations l10n) {
    switch (type) {
      case 'in':
        return l10n.stockInLabel;
      case 'out':
        return l10n.stockOutLabel;
      default:
        return type;
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final l10n = AppLocalizations.of(context);
    setState(() {
      _isLoading = true;
    });

    try {
      // Validate required fields
      if (_selectedProduct == null) {
        throw Exception('Vui lòng chọn sản phẩm');
      }

      final quantity = int.tryParse(_quantityController.text);
      if (quantity == null || quantity <= 0) {
        throw Exception('Số lượng không hợp lệ');
      }

      final unitPrice = double.tryParse(_priceController.text);
      if (unitPrice == null || unitPrice < 0) {
        throw Exception('Giá đơn vị không hợp lệ');
      }

      // Check stock for export transactions
      if (_transactionType == 'out') {
        final currentStock = _selectedProduct!['stock_quantity'] as int? ?? 0;
        if (quantity > currentStock) {
          throw Exception('Không đủ tồn kho. Tồn kho hiện tại: $currentStock');
        }
      }

      // Create transaction item from form data
      final items = <StockTransactionItem>[
        StockTransactionItem(
          id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
          transactionId: 'temp',
          productId: _selectedProduct!['id'].toString(),
          productName:
              _selectedProduct!['name']?.toString() ?? 'Unknown Product',
          quantity: quantity,
          unitPrice: unitPrice,
          totalPrice: quantity * unitPrice,
        ),
      ];

      // Determine transaction type
      final type = _transactionType == 'in'
          ? StockTransactionType.import
          : StockTransactionType.export;

      // Create transaction
      await StockTransactionService.createStockTransaction(
        type: type,
        partnerId: null,
        partnerName: null,
        createdBy:
            '00000000-0000-0000-0000-000000000001', // Temporary UUID for testing
        items: items,
        note: _noteController.text.isNotEmpty
            ? _noteController.text
            : 'Phiếu ${_transactionType == 'in' ? 'nhập' : 'xuất'} kho từ giao diện',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.stockTransactionCreatedSuccess),
            backgroundColor: Colors.green,
          ),
        );
        context.go(AppRoutes.stockTransactions);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${l10n.errorCreatingStockTransaction}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
