import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../data/services/invoice_service.dart';
import '../domain/entities/invoice.dart';
import 'widgets/invoice_filters.dart';
import 'widgets/invoice_list_widget.dart';
import 'widgets/invoice_stats_card.dart';

final invoicesProvider = StateNotifierProvider<InvoicesNotifier, InvoicesState>(
  (ref) {
    return InvoicesNotifier();
  },
);

class InvoicesState {
  final bool isLoading;
  final String? error;
  final List<Invoice> invoices;
  final Map<String, dynamic> statistics;
  final String selectedType;
  final String selectedStatus;
  final String searchQuery;

  const InvoicesState({
    this.isLoading = false,
    this.error,
    this.invoices = const [],
    this.statistics = const {},
    this.selectedType = 'all',
    this.selectedStatus = 'all',
    this.searchQuery = '',
  });

  InvoicesState copyWith({
    bool? isLoading,
    String? error,
    List<Invoice>? invoices,
    Map<String, dynamic>? statistics,
    String? selectedType,
    String? selectedStatus,
    String? searchQuery,
  }) {
    return InvoicesState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      invoices: invoices ?? this.invoices,
      statistics: statistics ?? this.statistics,
      selectedType: selectedType ?? this.selectedType,
      selectedStatus: selectedStatus ?? this.selectedStatus,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class InvoicesNotifier extends StateNotifier<InvoicesState> {
  InvoicesNotifier() : super(const InvoicesState()) {
    loadData();
  }

  Future<void> loadData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final invoices = await InvoiceService.getInvoices(
        type: state.selectedType == 'all' ? null : state.selectedType,
        status: state.selectedStatus == 'all' ? null : state.selectedStatus,
      );

      final statistics = await InvoiceService.getInvoiceStatistics();

      // Filter by search query
      final filteredInvoices = state.searchQuery.isEmpty
          ? invoices
          : invoices
                .where(
                  (invoice) =>
                      invoice.invoiceNumber.toLowerCase().contains(
                        state.searchQuery.toLowerCase(),
                      ) ||
                      (invoice.partnerName?.toLowerCase().contains(
                            state.searchQuery.toLowerCase(),
                          ) ??
                          false),
                )
                .toList();

      state = state.copyWith(
        isLoading: false,
        invoices: filteredInvoices,
        statistics: statistics,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> updateInvoiceStatus(String id, String status) async {
    try {
      await InvoiceService.updateInvoiceStatus(id, status);
      await loadData(); // Reload data
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> recordPayment(String id, double amount) async {
    try {
      await InvoiceService.recordPayment(id, amount);
      await loadData(); // Reload data
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deleteInvoice(String id) async {
    try {
      await InvoiceService.deleteInvoice(id);
      await loadData(); // Reload data
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  void setFilters({String? type, String? status, String? searchQuery}) {
    state = state.copyWith(
      selectedType: type ?? state.selectedType,
      selectedStatus: status ?? state.selectedStatus,
      searchQuery: searchQuery ?? state.searchQuery,
    );
    loadData();
  }
}

class InvoicesScreen extends ConsumerWidget {
  const InvoicesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(invoicesProvider);
    final responsive = Responsive(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: const Text('Hóa đơn'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.go(AppRoutes.pos),
            tooltip: 'Tạo hóa đơn mới (POS)',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(invoicesProvider.notifier).loadData(),
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () => ref.read(invoicesProvider.notifier).loadData(),
            )
          : _buildContent(context, ref, state, responsive),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    InvoicesState state,
    Responsive responsive,
  ) {
    if (responsive.isMobile) {
      return _buildMobileLayout(context, ref, state);
    } else {
      return _buildDesktopLayout(context, ref, state, responsive);
    }
  }

  Widget _buildMobileLayout(
    BuildContext context,
    WidgetRef ref,
    InvoicesState state,
  ) {
    return Column(
      children: [
        InvoiceFilters(
          selectedType: state.selectedType,
          selectedStatus: state.selectedStatus,
          searchQuery: state.searchQuery,
          onFiltersChanged: (type, status, search) {
            ref
                .read(invoicesProvider.notifier)
                .setFilters(type: type, status: status, searchQuery: search);
          },
        ),
        Expanded(child: InvoiceListWidget(invoices: state.invoices)),
      ],
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    WidgetRef ref,
    InvoicesState state,
    Responsive responsive,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 1,
                child: InvoiceStatsCard(statistics: state.statistics),
              ),
              const SizedBox(width: 24),
              Expanded(
                flex: 2,
                child: InvoiceFilters(
                  selectedType: state.selectedType,
                  selectedStatus: state.selectedStatus,
                  searchQuery: state.searchQuery,
                  onFiltersChanged: (type, status, search) {
                    ref
                        .read(invoicesProvider.notifier)
                        .setFilters(
                          type: type,
                          status: status,
                          searchQuery: search,
                        );
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Expanded(child: InvoiceListWidget(invoices: state.invoices)),
        ],
      ),
    );
  }
}
