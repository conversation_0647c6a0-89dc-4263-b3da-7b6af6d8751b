import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../../core/widgets/app_card.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../domain/entities/invoice.dart';

class InvoiceListWidget extends StatelessWidget {
  final List<Invoice> invoices;

  const InvoiceListWidget({super.key, required this.invoices});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                l10n.invoiceListTitle,
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                '${invoices.length} ${l10n.invoicesCountLabel}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (invoices.isEmpty)
            _buildEmptyState(context, l10n)
          else
            Expanded(child: _buildInvoiceList(context, l10n)),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, AppLocalizations l10n) {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 48,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            l10n.noInvoicesFound,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.createFirstInvoiceToStart,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceList(BuildContext context, AppLocalizations l10n) {
    return ListView.separated(
      itemCount: invoices.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final invoice = invoices[index];
        return _buildInvoiceItem(context, invoice, l10n);
      },
    );
  }

  Widget _buildInvoiceItem(
    BuildContext context,
    Invoice invoice,
    AppLocalizations l10n,
  ) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final dateFormat = DateFormat('dd/MM/yyyy');

    final statusColor = _getStatusColor(invoice.status);
    final typeIcon = _getTypeIcon(invoice.type);

    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: statusColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(typeIcon, color: statusColor, size: 20),
      ),
      title: LayoutBuilder(
        builder: (context, constraints) {
          // If too narrow, use column layout
          if (constraints.maxWidth < 200) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  invoice.invoiceNumber,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: MediaQuery.of(context).size.width < 600
                        ? 12.0
                        : 14.0,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4,
                    vertical: 1,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(3),
                  ),
                  child: Text(
                    _getStatusLabel(invoice.status, l10n),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                      fontSize: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 10.0,
                    ),
                  ),
                ),
              ],
            );
          } else {
            return Row(
              children: [
                Expanded(
                  child: Text(
                    invoice.invoiceNumber,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: MediaQuery.of(context).size.width < 600
                          ? 12.0
                          : 14.0,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 6),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: MediaQuery.of(context).size.width < 600
                        ? 4.0
                        : 6.0,
                    vertical: MediaQuery.of(context).size.width < 600
                        ? 1.0
                        : 2.0,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getStatusLabel(invoice.status, l10n),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                      fontSize: MediaQuery.of(context).size.width < 600
                          ? 8.0
                          : 10.0,
                    ),
                  ),
                ),
              ],
            );
          }
        },
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (invoice.partnerName != null) ...[
            Text(
              invoice.partnerName!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: MediaQuery.of(context).size.width < 600 ? 11.0 : 13.0,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
          ],
          Text(
            '${l10n.issueDate}: ${dateFormat.format(invoice.issueDate)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.withValues(alpha: 0.7),
              fontSize: MediaQuery.of(context).size.width < 600 ? 10.0 : 12.0,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (invoice.dueDate != null) ...[
            const SizedBox(height: 2),
            Text(
              '${l10n.dueDate}: ${dateFormat.format(invoice.dueDate!)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: invoice.isOverdue
                    ? Colors.red
                    : Colors.grey.withValues(alpha: 0.7),
                fontSize: MediaQuery.of(context).size.width < 600 ? 10.0 : 12.0,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
      trailing: SizedBox(
        width: MediaQuery.of(context).size.width < 600 ? 80 : 120,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: Text(
                currencyFormat.format(invoice.totalAmount),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: MediaQuery.of(context).size.width < 600
                      ? 11.0
                      : 14.0,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end,
              ),
            ),
            if (invoice.paidAmount > 0) ...[
              const SizedBox(height: 1),
              Flexible(
                child: Text(
                  '${l10n.paidAmountShort}: ${currencyFormat.format(invoice.paidAmount)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.w500,
                    fontSize: MediaQuery.of(context).size.width < 600
                        ? 7.0
                        : 9.0,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
            if (invoice.remainingAmount > 0) ...[
              const SizedBox(height: 1),
              Flexible(
                child: Text(
                  '${l10n.remainingAmount}: ${currencyFormat.format(invoice.remainingAmount)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.red,
                    fontWeight: FontWeight.w500,
                    fontSize: MediaQuery.of(context).size.width < 600
                        ? 7.0
                        : 9.0,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ],
        ),
      ),
      onTap: () => context.go('/invoice/${invoice.id}'),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'draft':
        return Colors.grey;
      case 'sent':
        return Colors.blue;
      case 'paid':
        return Colors.green;
      case 'overdue':
        return Colors.red;
      case 'cancelled':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'sale':
        return Icons.sell;
      case 'purchase':
        return Icons.shopping_cart;
      case 'return':
        return Icons.keyboard_return;
      default:
        return Icons.receipt;
    }
  }

  String _getStatusLabel(String status, AppLocalizations l10n) {
    switch (status) {
      case 'draft':
        return l10n.statusDraft;
      case 'sent':
        return l10n.statusSent;
      case 'paid':
        return l10n.statusPaid;
      case 'overdue':
        return l10n.statusOverdue;
      case 'cancelled':
        return l10n.statusCancelled;
      default:
        return status;
    }
  }
}
