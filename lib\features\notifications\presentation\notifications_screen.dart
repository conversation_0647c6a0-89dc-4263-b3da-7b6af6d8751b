import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/providers/notification_provider.dart';
import '../../../core/routers/app_router.dart';
import '../../../core/utils/responsive.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../data/services/notification_service.dart';
import '../domain/entities/notification.dart';
import 'widgets/notification_filters_widget.dart';
import 'widgets/notification_item_widget.dart';

final notificationsProvider =
    StateNotifierProvider<NotificationsNotifier, NotificationsState>((ref) {
      return NotificationsNotifier();
    });

class NotificationsState {
  final bool isLoading;
  final String? error;
  final List<AppNotification> notifications;
  final int unreadCount;
  final String selectedCategory;
  final String selectedType;
  final bool showOnlyUnread;

  const NotificationsState({
    this.isLoading = false,
    this.error,
    this.notifications = const [],
    this.unreadCount = 0,
    this.selectedCategory = 'all',
    this.selectedType = 'all',
    this.showOnlyUnread = false,
  });

  NotificationsState copyWith({
    bool? isLoading,
    String? error,
    List<AppNotification>? notifications,
    int? unreadCount,
    String? selectedCategory,
    String? selectedType,
    bool? showOnlyUnread,
  }) {
    return NotificationsState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      notifications: notifications ?? this.notifications,
      unreadCount: unreadCount ?? this.unreadCount,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      selectedType: selectedType ?? this.selectedType,
      showOnlyUnread: showOnlyUnread ?? this.showOnlyUnread,
    );
  }
}

class NotificationsNotifier extends StateNotifier<NotificationsState> {
  NotificationsNotifier() : super(const NotificationsState()) {
    loadNotifications();
  }

  Future<void> loadNotifications() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final notifications = await NotificationService.getNotifications(
        isRead: state.showOnlyUnread ? false : null,
        category: state.selectedCategory == 'all'
            ? null
            : state.selectedCategory,
        type: state.selectedType == 'all' ? null : state.selectedType,
      );

      final unreadCount = await NotificationService.getUnreadCount();

      state = state.copyWith(
        isLoading: false,
        notifications: notifications,
        unreadCount: unreadCount,
      );

      // Always refresh global unread count when loading notifications
      _refreshGlobalUnreadCount();
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void _refreshGlobalUnreadCount() {
    // Refresh the global unread count provider
    refreshGlobalUnreadCount();
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      await NotificationService.markAsRead(notificationId);
      await loadNotifications(); // Reload to update counts
      // Refresh global unread count
      _refreshGlobalUnreadCount();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> markAllAsRead() async {
    try {
      await NotificationService.markAllAsRead();
      await loadNotifications();
      _refreshGlobalUnreadCount();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      await NotificationService.deleteNotification(notificationId);
      await loadNotifications();
      _refreshGlobalUnreadCount();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deleteAllRead() async {
    try {
      await NotificationService.deleteAllRead();
      await loadNotifications();
      _refreshGlobalUnreadCount();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  void setFilters({String? category, String? type, bool? showOnlyUnread}) {
    state = state.copyWith(
      selectedCategory: category ?? state.selectedCategory,
      selectedType: type ?? state.selectedType,
      showOnlyUnread: showOnlyUnread ?? state.showOnlyUnread,
    );
    loadNotifications();
  }
}

class NotificationsScreen extends ConsumerWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(notificationsProvider);
    final responsive = Responsive(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: Row(
          children: [
            Text(l10n.notifications),
            if (state.unreadCount > 0) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${state.unreadCount}',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'mark_all_read':
                  ref.read(notificationsProvider.notifier).markAllAsRead();
                  break;
                case 'delete_all_read':
                  _showDeleteAllReadDialog(context, ref);
                  break;
                case 'settings':
                  _showNotificationSettings(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'mark_all_read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read, size: 16),
                    SizedBox(width: 8),
                    Text('Đánh dấu tất cả đã đọc'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete_all_read',
                child: Row(
                  children: [
                    Icon(Icons.delete_sweep, size: 16),
                    SizedBox(width: 8),
                    Text('Xóa tất cả đã đọc'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 16),
                    SizedBox(width: 8),
                    Text('Cài đặt thông báo'),
                  ],
                ),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () =>
                ref.read(notificationsProvider.notifier).loadNotifications(),
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () =>
                  ref.read(notificationsProvider.notifier).loadNotifications(),
            )
          : _buildContent(context, ref, state, responsive),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    NotificationsState state,
    Responsive responsive,
  ) {
    if (responsive.isMobile) {
      return _buildMobileLayout(context, ref, state);
    } else {
      return _buildDesktopLayout(context, ref, state);
    }
  }

  Widget _buildMobileLayout(
    BuildContext context,
    WidgetRef ref,
    NotificationsState state,
  ) {
    return Column(
      children: [
        NotificationFiltersWidget(
          selectedCategory: state.selectedCategory,
          selectedType: state.selectedType,
          showOnlyUnread: state.showOnlyUnread,
          onFiltersChanged: (category, type, showOnlyUnread) {
            ref
                .read(notificationsProvider.notifier)
                .setFilters(
                  category: category,
                  type: type,
                  showOnlyUnread: showOnlyUnread,
                );
          },
        ),
        Expanded(child: _buildNotificationsList(context, ref, state)),
      ],
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    WidgetRef ref,
    NotificationsState state,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          NotificationFiltersWidget(
            selectedCategory: state.selectedCategory,
            selectedType: state.selectedType,
            showOnlyUnread: state.showOnlyUnread,
            onFiltersChanged: (category, type, showOnlyUnread) {
              ref
                  .read(notificationsProvider.notifier)
                  .setFilters(
                    category: category,
                    type: type,
                    showOnlyUnread: showOnlyUnread,
                  );
            },
          ),
          const SizedBox(height: 24),
          Expanded(child: _buildNotificationsList(context, ref, state)),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(
    BuildContext context,
    WidgetRef ref,
    NotificationsState state,
  ) {
    if (state.notifications.isEmpty) {
      return _buildEmptyState(context);
    }

    return AppCard(
      child: ListView.separated(
        itemCount: state.notifications.length,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final notification = state.notifications[index];
          return NotificationItemWidget(
            notification: notification,
            onTap: () => _handleNotificationTap(context, ref, notification),
            onMarkAsRead: () => ref
                .read(notificationsProvider.notifier)
                .markAsRead(notification.id),
            onDelete: () => ref
                .read(notificationsProvider.notifier)
                .deleteNotification(notification.id),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            l10n.noNotifications,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.allNotificationsWillAppearHere,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  void _handleNotificationTap(
    BuildContext context,
    WidgetRef ref,
    AppNotification notification,
  ) {
    // Mark as read if not already read
    if (!notification.isRead) {
      ref.read(notificationsProvider.notifier).markAsRead(notification.id);
    }

    // Handle navigation based on notification category and type
    if (notification.category == 'sales' && notification.data != null) {
      // For new order notifications, navigate to invoices screen
      context.go(AppRoutes.invoices);
    } else if (notification.hasAction) {
      // For other notifications with action URL
      context.go(notification.actionUrl!);
    }
  }

  void _showDeleteAllReadDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa tất cả thông báo đã đọc'),
        content: const Text(
          'Bạn có chắc chắn muốn xóa tất cả thông báo đã đọc?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(notificationsProvider.notifier).deleteAllRead();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cài đặt thông báo'),
        content: const Text('Tính năng cài đặt thông báo sẽ có sẵn sớm.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }
}
