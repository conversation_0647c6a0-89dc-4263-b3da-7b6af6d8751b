import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/routers/app_router.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../generated/l10n/app_localizations.dart';
import '../data/services/partner_service.dart';
import '../domain/entities/partner.dart';
import 'partner_detail_screen.dart';
import 'widgets/partner_item_widget.dart';

// Simple Provider for Partners
final partnersSimpleProvider =
    StateNotifierProvider<PartnersSimpleNotifier, PartnersSimpleState>(
      (ref) => PartnersSimpleNotifier(),
    );

class PartnersSimpleState {
  final bool isLoading;
  final String? error;
  final List<Partner> partners;
  final String searchQuery;

  const PartnersSimpleState({
    this.isLoading = false,
    this.error,
    this.partners = const [],
    this.searchQuery = '',
  });

  PartnersSimpleState copyWith({
    bool? isLoading,
    String? error,
    List<Partner>? partners,
    String? searchQuery,
  }) {
    return PartnersSimpleState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      partners: partners ?? this.partners,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class PartnersSimpleNotifier extends StateNotifier<PartnersSimpleState> {
  PartnersSimpleNotifier() : super(const PartnersSimpleState()) {
    loadPartners();
  }

  Future<void> loadPartners() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final partners = await PartnerService.getPartners();
      state = state.copyWith(isLoading: false, partners: partners);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void searchPartners(String query) {
    state = state.copyWith(searchQuery: query);
  }

  Future<void> createPartner(Partner partner) async {
    try {
      await PartnerService.createPartner(partner);
      await loadPartners();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> updatePartner(String id, Partner partner) async {
    try {
      await PartnerService.updatePartner(id, partner);
      await loadPartners();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> deletePartner(String id) async {
    try {
      await PartnerService.deletePartner(id);
      await loadPartners();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }
}

class PartnersScreenSimple extends ConsumerWidget {
  const PartnersScreenSimple({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(partnersSimpleProvider);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
        title: Text(l10n.partnerManagement),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.go(AppRoutes.addPartner),
            tooltip: l10n.addPartnerTooltip,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () =>
                ref.read(partnersSimpleProvider.notifier).loadPartners(),
            tooltip: l10n.refreshTooltip,
          ),
        ],
      ),
      body: state.isLoading
          ? const LoadingWidget()
          : state.error != null
          ? AppErrorWidget(
              message: state.error!,
              onRetry: () =>
                  ref.read(partnersSimpleProvider.notifier).loadPartners(),
            )
          : _buildContent(context, ref, state),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    PartnersSimpleState state,
  ) {
    return Column(
      children: [
        // Stats Cards
        _buildStatsCards(context, state),

        // Search Bar
        _buildSearchBar(context, ref, state),

        // Partners List
        Expanded(child: _buildPartnersList(context, ref, state)),
      ],
    );
  }

  Widget _buildStatsCards(BuildContext context, PartnersSimpleState state) {
    final totalPartners = state.partners.length;
    final activePartners = state.partners.where((p) => p.isActive).length;
    final customers = state.partners
        .where(
          (p) => p.type == PartnerType.customer || p.type == PartnerType.both,
        )
        .length;
    final suppliers = state.partners
        .where(
          (p) => p.type == PartnerType.supplier || p.type == PartnerType.both,
        )
        .length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // If mobile screen, show hamburger menu
          if (MediaQuery.of(context).size.width < 768) {
            return _buildMobileStatsCards(
              context,
              totalPartners,
              activePartners,
              customers,
              suppliers,
            );
          } else {
            return _buildDesktopStatsCards(
              context,
              totalPartners,
              activePartners,
              customers,
              suppliers,
            );
          }
        },
      ),
    );
  }

  Widget _buildMobileStatsCards(
    BuildContext context,
    int totalPartners,
    int activePartners,
    int customers,
    int suppliers,
  ) {
    final l10n = AppLocalizations.of(context);
    return Row(
      children: [
        // Main stat card (most important)
        Expanded(
          flex: 3,
          child: _buildStatCard(
            context,
            l10n.totalPartners,
            totalPartners.toString(),
            Icons.people,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        // Hamburger menu for other stats
        PopupMenuButton<String>(
          icon: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(Icons.more_vert, color: Theme.of(context).primaryColor),
          ),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'active',
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          l10n.activePartners,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          activePartners.toString(),
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'customers',
              child: Row(
                children: [
                  Icon(Icons.person, color: Colors.orange, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          l10n.customersLabel,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          customers.toString(),
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'suppliers',
              child: Row(
                children: [
                  Icon(Icons.business, color: Colors.purple, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          l10n.suppliersLabel,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Text(
                          suppliers.toString(),
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: Colors.purple,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
          onSelected: (value) {
            // Optional: Show detailed dialog for each stat
            // _showStatDetails(context, value);
          },
        ),
      ],
    );
  }

  Widget _buildDesktopStatsCards(
    BuildContext context,
    int totalPartners,
    int activePartners,
    int customers,
    int suppliers,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            'Tổng đối tác',
            totalPartners.toString(),
            Icons.people,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            'Hoạt động',
            activePartners.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            'Khách hàng',
            customers.toString(),
            Icons.person,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            'Nhà cung cấp',
            suppliers.toString(),
            Icons.business,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(
    BuildContext context,
    WidgetRef ref,
    PartnersSimpleState state,
  ) {
    final l10n = AppLocalizations.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: TextField(
        decoration: InputDecoration(
          labelText: l10n.searchPartners,
          hintText: l10n.searchPartnerHint,
          prefixIcon: const Icon(Icons.search),
          border: const OutlineInputBorder(),
        ),
        onChanged: (value) {
          ref.read(partnersSimpleProvider.notifier).searchPartners(value);
        },
      ),
    );
  }

  Widget _buildPartnersList(
    BuildContext context,
    WidgetRef ref,
    PartnersSimpleState state,
  ) {
    final l10n = AppLocalizations.of(context);
    final filteredPartners = _getFilteredPartners(
      state.partners,
      state.searchQuery,
    );

    if (filteredPartners.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              state.searchQuery.isNotEmpty
                  ? l10n.noPartnersFound
                  : l10n.noPartnersYet,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              state.searchQuery.isNotEmpty
                  ? l10n.tryDifferentKeywords
                  : l10n.addFirstPartner,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => context.go(AppRoutes.addPartner),
              icon: const Icon(Icons.add),
              label: Text(l10n.addPartnerButton),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(partnersSimpleProvider.notifier).loadPartners(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredPartners.length,
        itemBuilder: (context, index) {
          final partner = filteredPartners[index];
          return PartnerItemWidget(
            partner: partner,
            onTap: () {
              print('🔗 Navigating to partner: ${partner.id}');
              // Try GoRouter first
              try {
                context.go('/partners/${partner.id}');
              } catch (e) {
                print('❌ GoRouter failed: $e');
                // Fallback to Navigator.push
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) =>
                        PartnerDetailScreen(partnerId: partner.id),
                  ),
                );
              }
            },
          );
        },
      ),
    );
  }

  List<Partner> _getFilteredPartners(List<Partner> partners, String query) {
    if (query.isEmpty) return partners;

    final lowerQuery = query.toLowerCase();
    return partners.where((partner) {
      return partner.name.toLowerCase().contains(lowerQuery) ||
          // Remove code search since it's not in database schema
          (partner.phone?.contains(query) ?? false) ||
          (partner.email?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }
}
