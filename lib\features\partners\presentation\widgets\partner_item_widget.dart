import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/widgets/app_card.dart';
import '../../domain/entities/partner.dart';

class PartnerItemWidget extends StatelessWidget {
  final Partner partner;
  final VoidCallback? onTap;

  const PartnerItemWidget({super.key, required this.partner, this.onTap});

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final dateFormat = DateFormat('dd/MM/yyyy');

    return AppCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  // Partner Type Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getTypeColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getTypeIcon(),
                      color: _getTypeColor(),
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Partner Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LayoutBuilder(
                          builder: (context, constraints) {
                            // If too narrow, use column layout
                            if (constraints.maxWidth < 200) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    partner.name,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontSize:
                                              MediaQuery.of(
                                                    context,
                                                  ).size.width <
                                                  600
                                              ? 13.0
                                              : 15.0,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 6.0
                                          : 8.0,
                                      vertical:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 1.0
                                          : 2.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getTypeColor().withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(
                                        MediaQuery.of(context).size.width < 600
                                            ? 8.0
                                            : 12.0,
                                      ),
                                    ),
                                    child: Text(
                                      partner.type.displayName,
                                      style: TextStyle(
                                        color: _getTypeColor(),
                                        fontSize:
                                            MediaQuery.of(context).size.width <
                                                600
                                            ? 10.0
                                            : 12.0,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            } else {
                              return Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      partner.name,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            fontSize:
                                                MediaQuery.of(
                                                      context,
                                                    ).size.width <
                                                    600
                                                ? 13.0
                                                : 15.0,
                                          ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  const SizedBox(width: 6),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 6.0
                                          : 8.0,
                                      vertical:
                                          MediaQuery.of(context).size.width <
                                              600
                                          ? 1.0
                                          : 2.0,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getTypeColor().withValues(
                                        alpha: 0.1,
                                      ),
                                      borderRadius: BorderRadius.circular(
                                        MediaQuery.of(context).size.width < 600
                                            ? 8.0
                                            : 12.0,
                                      ),
                                    ),
                                    child: Text(
                                      partner.type.displayName,
                                      style: TextStyle(
                                        color: _getTypeColor(),
                                        fontSize:
                                            MediaQuery.of(context).size.width <
                                                600
                                            ? 10.0
                                            : 12.0,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            }
                          },
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Mã: ${partner.displayCode}',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: Colors.grey[600],
                                fontSize:
                                    MediaQuery.of(context).size.width < 600
                                    ? 11.0
                                    : 13.0,
                              ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // Status Badge
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: MediaQuery.of(context).size.width < 600
                          ? 6.0
                          : 8.0,
                      vertical: MediaQuery.of(context).size.width < 600
                          ? 2.0
                          : 4.0,
                    ),
                    decoration: BoxDecoration(
                      color: partner.isActive
                          ? Colors.green.withOpacity(0.1)
                          : Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(
                        MediaQuery.of(context).size.width < 600 ? 8.0 : 12.0,
                      ),
                    ),
                    child: Text(
                      partner.isActive ? 'Hoạt động' : 'Ngưng',
                      style: TextStyle(
                        color: partner.isActive ? Colors.green : Colors.red,
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 10.0
                            : 12.0,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Contact Info
              Row(
                children: [
                  if (partner.phone != null) ...[
                    Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Text(
                      partner.phone!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: MediaQuery.of(context).size.width < 600
                            ? 12.0
                            : 14.0,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(width: 16),
                  ],
                  if (partner.email != null) ...[
                    Icon(Icons.email, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        partner.email!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: MediaQuery.of(context).size.width < 600
                              ? 12.0
                              : 14.0,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ],
              ),

              // Address
              if (partner.fullAddress.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        partner.fullAddress,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              ],

              // Financial Info
              if (partner.creditLimit > 0 || partner.currentBalance > 0) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 8),
                Row(
                  children: [
                    if (partner.creditLimit > 0) ...[
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Hạn mức',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                            Text(
                              currencyFormat.format(partner.creditLimit),
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                    ],
                    if (partner.currentBalance > 0) ...[
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Công nợ',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                            Text(
                              currencyFormat.format(partner.currentBalance),
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: partner.isOverCreditLimit
                                        ? Colors.red
                                        : Colors.orange,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],

              // Created Date
              const SizedBox(height: 8),
              Text(
                'Tạo: ${dateFormat.format(partner.createdAt)}',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getTypeColor() {
    switch (partner.type) {
      case PartnerType.customer:
        return Colors.blue;
      case PartnerType.supplier:
        return Colors.green;
      case PartnerType.both:
        return Colors.purple;
    }
  }

  IconData _getTypeIcon() {
    switch (partner.type) {
      case PartnerType.customer:
        return Icons.person;
      case PartnerType.supplier:
        return Icons.business;
      case PartnerType.both:
        return Icons.group;
    }
  }
}
