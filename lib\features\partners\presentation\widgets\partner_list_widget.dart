import 'package:flutter/material.dart';

import '../../../../core/widgets/app_card.dart';
import '../../../../data/models/partner.dart';

class PartnerListWidget extends StatelessWidget {
  final List<Partner> partners;
  final Function(Partner) onEdit;
  final Function(Partner) onDelete;

  const PartnerListWidget({
    super.key,
    required this.partners,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Danh sách đối tác',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                '${partners.length} đối tác',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (partners.isEmpty)
            _buildEmptyState(context)
          else
            Expanded(child: _buildPartnerList(context)),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 48,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Chưa có đối tác nào',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Thêm đối tác đầu tiên để bắt đầu',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPartnerList(BuildContext context) {
    return ListView.separated(
      itemCount: partners.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final partner = partners[index];
        return _buildPartnerItem(context, partner);
      },
    );
  }

  Widget _buildPartnerItem(BuildContext context, Partner partner) {
    final isCustomer = partner.type == 'customer';
    final icon = isCustomer ? Icons.person : Icons.business;
    final color = isCustomer ? Colors.blue : Colors.green;

    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(
        partner.name,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isCustomer ? 'Khách hàng' : 'Nhà cung cấp',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (partner.email != null) ...[
            const SizedBox(height: 2),
            Text(
              partner.email!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.withValues(alpha: 0.7),
              ),
            ),
          ],
          if (partner.phone != null) ...[
            const SizedBox(height: 2),
            Text(
              partner.phone!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.withValues(alpha: 0.7),
              ),
            ),
          ],
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: partner.isActive
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              partner.isActive ? 'Hoạt động' : 'Không hoạt động',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: partner.isActive ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  onEdit(partner);
                  break;
                case 'delete':
                  onDelete(partner);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16),
                    SizedBox(width: 8),
                    Text('Chỉnh sửa'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Xóa', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      onTap: () => _showPartnerDetails(context, partner),
    );
  }

  void _showPartnerDetails(BuildContext context, Partner partner) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chi tiết đối tác'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Tên:', partner.name),
            _buildDetailRow(
              'Loại:',
              partner.type == 'customer' ? 'Khách hàng' : 'Nhà cung cấp',
            ),
            if (partner.email != null)
              _buildDetailRow('Email:', partner.email!),
            if (partner.phone != null)
              _buildDetailRow('Điện thoại:', partner.phone!),
            if (partner.address != null)
              _buildDetailRow('Địa chỉ:', partner.address!),
            if (partner.taxNumber != null)
              _buildDetailRow('Mã số thuế:', partner.taxNumber!),
            _buildDetailRow(
              'Trạng thái:',
              partner.isActive ? 'Hoạt động' : 'Không hoạt động',
            ),
            if (partner.notes != null)
              _buildDetailRow('Ghi chú:', partner.notes!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onEdit(partner);
            },
            child: const Text('Chỉnh sửa'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
