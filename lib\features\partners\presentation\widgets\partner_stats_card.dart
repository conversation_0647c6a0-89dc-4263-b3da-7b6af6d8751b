import 'package:flutter/material.dart';

import '../../../../core/widgets/app_card.dart';
import '../../../../data/models/partner.dart';

class PartnerStatsCard extends StatelessWidget {
  final List<Partner> partners;

  const PartnerStatsCard({
    super.key,
    required this.partners,
  });

  @override
  Widget build(BuildContext context) {
    final customers = partners.where((p) => p.isCustomer).length;
    final suppliers = partners.where((p) => p.isSupplier).length;
    final activePartners = partners.where((p) => p.isActive).length;
    final totalBalance = partners.fold<double>(0, (sum, p) => sum + p.currentBalance);

    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thống kê đối tác',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildStatsGrid(context, customers, suppliers, activePartners, totalBalance),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(
    BuildContext context,
    int customers,
    int suppliers,
    int activePartners,
    double totalBalance,
  ) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                context,
                'Khách hàng',
                customers.toString(),
                Icons.person,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatItem(
                context,
                'Nhà cung cấp',
                suppliers.toString(),
                Icons.business,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                context,
                'Đang hoạt động',
                activePartners.toString(),
                Icons.check_circle,
                Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatItem(
                context,
                'Tổng công nợ',
                '${(totalBalance / 1000000).toStringAsFixed(1)}M',
                Icons.account_balance_wallet,
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
