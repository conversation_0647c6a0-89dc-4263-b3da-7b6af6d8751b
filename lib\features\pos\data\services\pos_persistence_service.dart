import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../presentation/pos_screen_v2.dart';

class POSPersistenceService {
  static const String _tabsKey = 'pos_tabs';
  static const String _activeTabIndexKey = 'pos_active_tab_index';

  // Save tabs to local storage
  static Future<void> saveTabs(
    List<POSTabData> tabs,
    int activeTabIndex,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert tabs to JSON
      final tabsJson = tabs
          .map(
            (tab) => {
              'id': tab.id,
              'name': tab.name,
              'cartItems': tab.cartItems
                  .map(
                    (item) => {
                      'productId': item.product.id,
                      'productName': item.product.name,
                      'productCode': item.product.code,
                      'productSku': item.product.sku,
                      'productSalePrice': item.product.salePrice,
                      'productStockQuantity': item.product.stockQuantity,
                      'productImageUrl': item.product.imageUrl,
                      'quantity': item.quantity,
                    },
                  )
                  .toList(),
              'customerName': tab.customerName,
              'customerPhone': tab.customerPhone,
              'discount': tab.discount,
              'paymentMethod': tab.paymentMethod,
            },
          )
          .toList();

      await prefs.setString(_tabsKey, jsonEncode(tabsJson));
      await prefs.setInt(_activeTabIndexKey, activeTabIndex);

      print('✅ POS tabs saved successfully');
    } catch (e) {
      print('❌ Error saving POS tabs: $e');
    }
  }

  // Load tabs from local storage
  static Future<Map<String, dynamic>?> loadTabs() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final tabsJsonString = prefs.getString(_tabsKey);
      final activeTabIndex = prefs.getInt(_activeTabIndexKey) ?? 0;

      if (tabsJsonString == null) {
        print('📝 No saved POS tabs found');
        return null;
      }

      final tabsJson = jsonDecode(tabsJsonString) as List;

      final tabs = tabsJson.map((tabJson) {
        final cartItems = (tabJson['cartItems'] as List).map((itemJson) {
          final product = SimpleProduct(
            id: itemJson['productId'],
            name: itemJson['productName'],
            code: itemJson['productCode'],
            sku: itemJson['productSku'],
            salePrice: itemJson['productSalePrice']?.toDouble() ?? 0.0,
            stockQuantity: itemJson['productStockQuantity'] ?? 0,
            imageUrl: itemJson['productImageUrl'],
          );

          return CartItem(
            product: product,
            quantity: itemJson['quantity'] ?? 1,
          );
        }).toList();

        return POSTabData(
          id: tabJson['id'],
          name: tabJson['name'],
          cartItems: cartItems,
          customerName: tabJson['customerName'],
          customerPhone: tabJson['customerPhone'],
          discount: tabJson['discount']?.toDouble() ?? 0.0,
          paymentMethod: tabJson['paymentMethod'] ?? 'cash',
        );
      }).toList();

      print('✅ POS tabs loaded successfully: ${tabs.length} tabs');

      return {'tabs': tabs, 'activeTabIndex': activeTabIndex};
    } catch (e) {
      print('❌ Error loading POS tabs: $e');
      return null;
    }
  }

  // Clear saved tabs
  static Future<void> clearTabs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tabsKey);
      await prefs.remove(_activeTabIndexKey);
      print('✅ POS tabs cleared successfully');
    } catch (e) {
      print('❌ Error clearing POS tabs: $e');
    }
  }

  // Save single tab (for real-time updates)
  static Future<void> saveTabsQuick(
    List<POSTabData> tabs,
    int activeTabIndex,
  ) async {
    // Use the same method but without logging for performance
    try {
      final prefs = await SharedPreferences.getInstance();

      final tabsJson = tabs
          .map(
            (tab) => {
              'id': tab.id,
              'name': tab.name,
              'cartItems': tab.cartItems
                  .map(
                    (item) => {
                      'productId': item.product.id,
                      'productName': item.product.name,
                      'productCode': item.product.code,
                      'productSku': item.product.sku,
                      'productSalePrice': item.product.salePrice,
                      'productStockQuantity': item.product.stockQuantity,
                      'productImageUrl': item.product.imageUrl,
                      'quantity': item.quantity,
                    },
                  )
                  .toList(),
              'customerName': tab.customerName,
              'customerPhone': tab.customerPhone,
              'discount': tab.discount,
              'paymentMethod': tab.paymentMethod,
            },
          )
          .toList();

      await prefs.setString(_tabsKey, jsonEncode(tabsJson));
      await prefs.setInt(_activeTabIndexKey, activeTabIndex);
    } catch (e) {
      // Silent fail for performance
    }
  }
}
