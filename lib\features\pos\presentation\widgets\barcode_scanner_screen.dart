// Temporarily commented out entire file due to mobile_scanner dependency issue
import 'package:flutter/material.dart';

/*
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:mobile_scanner/mobile_scanner.dart'; // Commented out due to version conflict

import '../../../../core/services/product_service.dart';
import '../../../../core/widgets/product_image_widget.dart';
import '../../../../data/models/product.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../pos_screen_v2.dart';

class BarcodeScannerScreen extends ConsumerStatefulWidget {
  const BarcodeScannerScreen({super.key});

  @override
  ConsumerState<BarcodeScannerScreen> createState() =>
      _BarcodeScannerScreenState();
}

class _BarcodeScannerScreenState extends ConsumerState<BarcodeScannerScreen> {
  // final MobileScannerController controller = MobileScannerController();
  final List<ScannedProduct> scannedProducts = [];
  bool isProcessing = false;

  @override
  void dispose() {
    // controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.scanBarcode),
        actions: [
          IconButton(
            icon: const Icon(Icons.flash_on),
            onPressed: () => controller.toggleTorch(),
          ),
          IconButton(
            icon: const Icon(Icons.camera_rear),
            onPressed: () => controller.switchCamera(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Scanner View (Camera)
          Expanded(
            flex: 2,
            child: ClipRect(
              child: MobileScanner(
                controller: controller,
                onDetect: _onBarcodeDetected,
              ),
            ),
          ),

          // Divider
          const Divider(height: 1),

          // Scanned Products List
          Expanded(
            flex: 1,
            child: scannedProducts.isEmpty
                ? Center(
                    child: Text(
                      'Quét mã vạch để thêm sản phẩm',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  )
                : _buildScannedProductsList(context),
          ),

          // Bottom Actions
          _buildBottomActions(context, l10n),
        ],
      ),
    );
  }

  Widget _buildScannedProductsList(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: scannedProducts.length,
      itemBuilder: (context, index) {
        final item = scannedProducts[index];

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: item.product.id != null
                ? SizedBox(
                    width: 40,
                    height: 40,
                    child: ProductImageWidget(
                      productId: item.product.id!,
                      initialImageUrl: item.product.imageUrl,
                      isEditable: false,
                      borderRadius: BorderRadius.circular(4),
                      fit: BoxFit.cover,
                    ),
                  )
                : const Icon(Icons.inventory, size: 40),
            title: Text(item.product.name),
            subtitle: Text('${item.product.price.toStringAsFixed(0)}₫'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.remove_circle_outline),
                  onPressed: () => _updateQuantity(index, item.quantity - 1),
                ),
                Text(
                  '${item.quantity}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                IconButton(
                  icon: const Icon(Icons.add_circle_outline),
                  onPressed: () => _updateQuantity(index, item.quantity + 1),
                ),
                IconButton(
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                  onPressed: () => _removeScannedProduct(index),
                ),
              ],
            ),
            isThreeLine: false,
          ),
        );
      },
    );
  }

  Widget _buildBottomActions(BuildContext context, AppLocalizations l10n) {
    final totalItems = scannedProducts.fold(
      0,
      (sum, item) => sum + item.quantity,
    );
    final totalAmount = scannedProducts.fold(
      0.0,
      (sum, item) => sum + (item.product.price * item.quantity),
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Tổng sản phẩm: $totalItems',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              Text(
                '${l10n.total}: ${totalAmount.toStringAsFixed(0)}₫',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      scannedProducts.clear();
                    });
                  },
                  child: Text(l10n.clearAll),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: scannedProducts.isEmpty || isProcessing
                      ? null
                      : _addAllToCart,
                  child: isProcessing
                      ? const CircularProgressIndicator.adaptive()
                      : Text('Tạo đơn hàng'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _onBarcodeDetected(BarcodeCapture capture) async {
    if (isProcessing) return;

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isEmpty) return;

    // Get the first barcode
    final String barcodeValue = barcodes.first.rawValue ?? '';
    if (barcodeValue.isEmpty) return;

    // Temporarily pause scanning
    await controller.stop();
    setState(() {
      isProcessing = true;
    });

    try {
      // Search for product with this barcode
      final product = await ProductService.getProductByBarcode(barcodeValue);

      if (product != null) {
        // Product found, check if it's already in the list
        final existingIndex = scannedProducts.indexWhere(
          (item) => item.product.id == product.id,
        );

        setState(() {
          if (existingIndex >= 0) {
            // Update quantity of existing product
            scannedProducts[existingIndex] = ScannedProduct(
              product: product,
              quantity: scannedProducts[existingIndex].quantity + 1,
              barcode: barcodeValue,
            );
          } else {
            // Add new product
            scannedProducts.add(
              ScannedProduct(
                product: product,
                quantity: 1,
                barcode: barcodeValue,
              ),
            );
          }
        });

        // Show confirmation
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${product.name} đã được thêm vào giỏ hàng'),
            duration: const Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        // Product not found
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Không tìm thấy sản phẩm'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Lỗi: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      setState(() {
        isProcessing = false;
      });

      // Resume scanning after a short delay
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          controller.start();
        }
      });
    }
  }

  void _updateQuantity(int index, int newQuantity) {
    if (newQuantity <= 0) {
      _removeScannedProduct(index);
      return;
    }

    setState(() {
      scannedProducts[index] = ScannedProduct(
        product: scannedProducts[index].product,
        quantity: newQuantity,
        barcode: scannedProducts[index].barcode,
      );
    });
  }

  void _removeScannedProduct(int index) {
    setState(() {
      scannedProducts.removeAt(index);
    });
  }

  void _addAllToCart() {
    if (scannedProducts.isEmpty) return;

    final notifier = ref.read(posProviderV2.notifier);

    // Add all scanned products to cart
    for (final item in scannedProducts) {
      // Convert Product to SimpleProduct
      final simpleProduct = SimpleProduct(
        id: item.product.id ?? '',
        name: item.product.name,
        code: item.product.sku,
        sku: item.product.barcode,
        salePrice: item.product.price,
        stockQuantity: item.product.stockQuantity,
        imageUrl: item.product.imageUrl,
      );

      // Add to cart multiple times for quantity
      for (int i = 0; i < item.quantity; i++) {
        notifier.addToCart(simpleProduct);
      }
    }

    // Return to POS screen
    Navigator.of(context).pop();

    // Show confirmation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sản phẩm đã được thêm vào giỏ hàng'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

class ScannedProduct {
  final Product product;
  final int quantity;
  final String barcode;

  const ScannedProduct({
    required this.product,
    required this.quantity,
    required this.barcode,
  });
}
*/

// Placeholder class to avoid compilation errors
class BarcodeScannerScreen extends StatelessWidget {
  const BarcodeScannerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Barcode Scanner')),
      body: const Center(
        child: Text('Barcode scanner temporarily disabled'),
      ),
    );
  }
}
