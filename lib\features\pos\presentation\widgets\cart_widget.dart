import 'package:city_pos/features/pos/presentation/pos_screen_v2.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/utils/responsive.dart';
import '../../../../generated/l10n/app_localizations.dart';

class CartWidget extends StatelessWidget {
  final List<CartItem> cartItems;
  final double subtotal;
  final double tax;
  final double total;
  final bool isProcessingPayment;
  final Function(int, int) onUpdateQuantity;
  final Function(int) onRemoveItem;
  final VoidCallback onClearCart;
  final VoidCallback onCheckout;

  const CartWidget({
    super.key,
    required this.cartItems,
    required this.subtotal,
    required this.tax,
    required this.total,
    required this.isProcessingPayment,
    required this.onUpdateQuantity,
    required this.onRemoveItem,
    required this.onClearCart,
    required this.onCheckout,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Column(
      children: [
        // Header
        Container(
          padding: Responsive.responsivePadding(context),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(Responsive.responsiveBorderRadius(context)),
            ),
          ),
          child: Row(
            children: [
              Icon(Icons.shopping_cart, color: Colors.white),
              const SizedBox(width: 8),
              Text(
                '${l10n.cart} (${cartItems.length})',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (cartItems.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.clear_all, color: Colors.white),
                  onPressed: onClearCart,
                  tooltip: l10n.clearAllTooltip,
                ),
            ],
          ),
        ),

        // Cart Items
        Expanded(
          child: cartItems.isEmpty
              ? _buildEmptyCart(context)
              : ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: cartItems.length,
                  itemBuilder: (context, index) {
                    final item = cartItems[index];
                    return CartItemCard(
                      item: item,
                      onUpdateQuantity: (quantity) =>
                          onUpdateQuantity(index, quantity),
                      onRemove: () => onRemoveItem(index),
                    );
                  },
                ),
        ),

        // Summary
        if (cartItems.isNotEmpty) ...[
          const Divider(height: 1),
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Subtotal
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('${l10n.subtotal}:'),
                    Text(currencyFormat.format(subtotal)),
                  ],
                ),
                const SizedBox(height: 4),

                // Tax
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('${l10n.tax} (10%):'),
                    Text(currencyFormat.format(tax)),
                  ],
                ),
                const SizedBox(height: 8),
                const Divider(),

                // Total
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${l10n.total}:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      currencyFormat.format(total),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Checkout Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: isProcessingPayment ? null : onCheckout,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: isProcessingPayment
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : Text(
                            l10n.payment,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildEmptyCart(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            l10n.emptyCart,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.addProductsToStart,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }
}

class CartItemCard extends StatelessWidget {
  final CartItem item;
  final Function(int) onUpdateQuantity;
  final VoidCallback onRemove;

  const CartItemCard({
    super.key,
    required this.item,
    required this.onUpdateQuantity,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Name and Remove Button
            Row(
              children: [
                Expanded(
                  child: Text(
                    item.product.name,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 18),
                  onPressed: onRemove,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Price and Quantity Controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Unit Price
                Text(
                  currencyFormat.format(item.unitPrice),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.withValues(alpha: 0.7),
                    fontSize: Responsive.responsiveFontSize(
                      context,
                      mobile: 11.0,
                      tablet: 12.0,
                      desktop: 13.0,
                    ),
                  ),
                ),

                // Quantity Controls
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(
                      Responsive.responsiveBorderRadius(context) * 0.5,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      InkWell(
                        onTap: () {
                          if (item.quantity > 1) {
                            onUpdateQuantity(item.quantity - 1);
                          }
                        },
                        child: Container(
                          padding: EdgeInsets.all(
                            Responsive.responsiveSpacing(context) * 0.5,
                          ),
                          child: Icon(
                            Icons.remove,
                            size: Responsive.responsiveIconSize(context) * 0.6,
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Responsive.responsiveSpacing(context),
                          vertical: Responsive.responsiveSpacing(context) * 0.5,
                        ),
                        child: Text(
                          '${item.quantity}',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: Responsive.responsiveFontSize(
                              context,
                              mobile: 12.0,
                              tablet: 13.0,
                              desktop: 14.0,
                            ),
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () => onUpdateQuantity(item.quantity + 1),
                        child: Container(
                          padding: EdgeInsets.all(
                            Responsive.responsiveSpacing(context) * 0.5,
                          ),
                          child: Icon(
                            Icons.add,
                            size: Responsive.responsiveIconSize(context) * 0.6,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Total Price
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${l10n.lineItemTotalLabel}:',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                Text(
                  currencyFormat.format(item.totalPrice),
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
