import 'package:flutter/material.dart';

import '../../../../core/utils/responsive.dart';
import '../../../../core/widgets/product_image_widget.dart';
import '../../../../data/models/product.dart';
import '../../../../generated/l10n/app_localizations.dart';

class ProductGridWidget extends StatelessWidget {
  final List<Product> products;
  final Function(Product) onProductTap;

  const ProductGridWidget({
    super.key,
    required this.products,
    required this.onProductTap,
  });

  @override
  Widget build(BuildContext context) {
    if (products.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context).noProductsFound,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: Responsive.responsivePadding(context),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: Responsive.responsiveProductGridCount(context),
        crossAxisSpacing: Responsive.responsiveSpacing(context),
        mainAxisSpacing: Responsive.responsiveSpacing(context),
        childAspectRatio: Responsive.responsiveCardAspectRatio(context),
      ),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return ProductCard(
          product: product,
          onTap: () => onProductTap(product),
        );
      },
    );
  }
}

class ProductCard extends StatelessWidget {
  final Product product;
  final VoidCallback onTap;

  const ProductCard({super.key, required this.product, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive(context);

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 3,
              child: product.id != null
                  ? ProductImageWidget(
                      productId: product.id!,
                      initialImageUrl: product.imageUrl,
                      isEditable: false,
                      fit: BoxFit.cover,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    )
                  : _buildPlaceholderImage(),
            ),

            // Product Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: EdgeInsets.all(
                  Responsive(context).isMobile ? 6.0 : 8.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Product Name
                    Flexible(
                      child: Text(
                        product.name,
                        style: Responsive(context).isMobile
                            ? Theme.of(context).textTheme.labelLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              )
                            : Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        maxLines: Responsive(context).isMobile ? 1 : 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(height: Responsive(context).isMobile ? 2.0 : 4.0),

                    // SKU - Always show if available
                    if (product.sku != null && product.sku!.isNotEmpty)
                      Flexible(
                        child: Text(
                          'SKU: ${product.sku}',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                    if (product.sku != null && product.sku!.isNotEmpty)
                      SizedBox(
                        height: Responsive(context).isMobile ? 2.0 : 4.0,
                      ),

                    // Spacer - Use Flexible instead of Spacer for better control
                    const Flexible(child: SizedBox(height: 4)),

                    // Price and Stock
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            product.formattedPrice,
                            style: Responsive(context).isMobile
                                ? Theme.of(
                                    context,
                                  ).textTheme.labelMedium?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  )
                                : Theme.of(
                                    context,
                                  ).textTheme.titleMedium?.copyWith(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildStockBadge(context),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Icon(
        Icons.inventory_2_outlined,
        size: 48,
        color: Colors.grey[600],
      ),
    );
  }

  Widget _buildStockBadge(BuildContext context) {
    final responsive = Responsive(context);
    Color badgeColor;
    String stockText;

    if (product.isOutOfStock) {
      badgeColor = Colors.red;
      stockText = '0';
    } else if (product.isLowStock) {
      badgeColor = Colors.orange;
      stockText = '${product.stockQuantity}';
    } else {
      badgeColor = Colors.green;
      stockText = '${product.stockQuantity}';
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: responsive.isMobile ? 4.0 : 6.0,
        vertical: responsive.isMobile ? 1.0 : 2.0,
      ),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(responsive.isMobile ? 3.0 : 4.0),
        border: Border.all(color: badgeColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        stockText,
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          color: badgeColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
