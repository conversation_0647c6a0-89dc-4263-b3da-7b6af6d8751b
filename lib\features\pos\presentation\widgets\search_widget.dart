import 'package:flutter/material.dart';

import '../../../../generated/l10n/app_localizations.dart';
import 'barcode_scanner_screen.dart';

class SearchWidget extends StatelessWidget {
  final String searchQuery;
  final Function(String) onSearchChanged;

  const SearchWidget({
    super.key,
    required this.searchQuery,
    required this.onSearchChanged,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return TextField(
      decoration: InputDecoration(
        hintText: l10n.searchProductHint,
        prefixIcon: const Icon(Icons.search),
        suffixIcon: searchQuery.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () => onSearchChanged(''),
              )
            : IconButton(
                icon: const Icon(Icons.qr_code_scanner),
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const BarcodeScannerScreen(),
                    ),
                  );
                },
              ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey.withOpacity(0.1),
      ),
      onChanged: onSearchChanged,
    );
  }
}
