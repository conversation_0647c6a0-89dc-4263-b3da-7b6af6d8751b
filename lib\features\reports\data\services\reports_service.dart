import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/config/supabase_config.dart';
import '../../domain/entities/report_data.dart';

class ReportsService {
  static final SupabaseClient? _supabase = SupabaseConfig.client;

  // Get sales report
  static Future<SalesReportData> getSalesReport({
    required DateTime startDate,
    required DateTime endDate,
    ReportPeriod period = ReportPeriod.daily,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoSalesReport(startDate, endDate);
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final startDateStr = startDate.toIso8601String().split('T')[0];
      final endDateStr = endDate.toIso8601String().split('T')[0];

      print('=== SALES REPORT DEBUG ===');
      print('Start Date: $startDateStr');
      print('End Date: $endDateStr');

      // First check if there are ANY orders in database
      final allOrdersCheck = await _supabase!
          .from('orders')
          .select('id, status, created_at')
          .limit(5);

      print('Total orders in database: ${allOrdersCheck.length}');
      for (final order in allOrdersCheck) {
        print(
          'Order: ${order['id']}, status: ${order['status']}, created: ${order['created_at']}',
        );
      }

      // Also check products
      final productsCheck = await _supabase!
          .from('products')
          .select('id, name, price')
          .limit(3);

      print('Total products in database: ${productsCheck.length}');
      for (final product in productsCheck) {
        print('Product: ${product['name']}, price: ${product['price']}');
      }

      // Get orders data - try all orders first to debug
      var ordersResponse = await _supabase!
          .from('orders')
          .select(
            'id, total_amount, subtotal, tax_amount, payment_method, created_at, status, order_items(*)',
          );

      print('All orders in database: ${ordersResponse.length}');
      for (final order in ordersResponse.take(3)) {
        print(
          'Order: ${order['id']}, status: ${order['status']}, created: ${order['created_at']}, total: ${order['total_amount']}',
        );
      }

      // Filter by date range
      final filteredOrders = ordersResponse.where((order) {
        final createdAt = DateTime.parse(order['created_at']);
        final isInRange =
            createdAt.isAfter(startDate.subtract(Duration(days: 1))) &&
            createdAt.isBefore(endDate.add(Duration(days: 1)));
        print('Order ${order['id']}: created=$createdAt, inRange=$isInRange');
        return isInRange;
      }).toList();

      print('Orders in date range: ${filteredOrders.length}');
      ordersResponse = filteredOrders;

      // Calculate totals
      double totalRevenue = 0;
      double totalCost = 0;
      int totalOrders = ordersResponse.length;
      int totalItems = 0;
      Map<String, int> ordersByPaymentMethod = {};

      for (final order in ordersResponse) {
        final orderTotal = (order['total_amount'] as num?)?.toDouble() ?? 0;
        final orderSubtotal = (order['subtotal'] as num?)?.toDouble() ?? 0;

        totalRevenue += orderTotal;
        totalCost += orderSubtotal * 0.6; // Assume 60% cost ratio

        final paymentMethod = order['payment_method'] as String? ?? 'cash';
        ordersByPaymentMethod[paymentMethod] =
            (ordersByPaymentMethod[paymentMethod] ?? 0) + 1;

        final orderItems = order['order_items'] as List? ?? [];
        final itemsCount = orderItems.fold<int>(
          0,
          (sum, item) => sum + (item['quantity'] as int? ?? 0),
        );
        totalItems += itemsCount;

        print('Order ${order['id']}: total=$orderTotal, items=$itemsCount');
      }

      print(
        'Final totals: revenue=$totalRevenue, orders=${ordersResponse.length}, items=$totalItems',
      );

      final grossProfit = totalRevenue - totalCost;
      final averageOrderValue = totalOrders > 0
          ? (totalRevenue / totalOrders).toDouble()
          : 0.0;

      // Get top selling products
      final topProducts = await getTopSellingProducts(
        startDate: startDate,
        endDate: endDate,
        limit: 5,
      );

      return SalesReportData(
        date: DateTime.now(),
        totalRevenue: totalRevenue,
        totalCost: totalCost,
        grossProfit: grossProfit,
        totalOrders: totalOrders,
        totalItems: totalItems,
        averageOrderValue: averageOrderValue,
        revenueByCategory: {
          'Tổng doanh thu': totalRevenue,
        }, // Simplified for now
        ordersByPaymentMethod: ordersByPaymentMethod,
        topProducts: topProducts,
      );
    } catch (e) {
      print('Error getting sales report: $e');
      return _getDemoSalesReport(startDate, endDate);
    }
  }

  // Get inventory report
  static Future<InventoryReportData> getInventoryReport({
    DateTime? asOfDate,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoInventoryReport(asOfDate ?? DateTime.now());
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      // Get products data with category join
      final productsResponse = await _supabase!.from('products').select('''
            id, name, stock_quantity, price, cost, category_id,
            categories(name)
          ''');

      int totalProducts = productsResponse.length;
      double totalStockValue = 0;
      int lowStockProducts = 0;
      int outOfStockProducts = 0;
      List<ProductStockInfo> productStocks = [];
      Map<String, double> stockValueByCategory = {};

      for (final product in productsResponse) {
        final stockQuantity = (product['stock_quantity'] as num?)?.toInt() ?? 0;
        final costPrice = (product['cost'] as num?)?.toDouble() ?? 0;
        final category =
            product['categories']?['name'] as String? ?? 'Chưa phân loại';

        final stockValue = stockQuantity * costPrice;
        totalStockValue += stockValue;

        // Count low stock and out of stock
        if (stockQuantity == 0) {
          outOfStockProducts++;
        } else if (stockQuantity <= 10) {
          // Assume low stock threshold is 10
          lowStockProducts++;
        }

        // Add to product stocks
        productStocks.add(
          ProductStockInfo(
            productId: product['id'],
            productName: product['name'],
            category: category,
            currentStock: stockQuantity,
            minStock: 10, // Default minimum stock threshold
            unitCost: costPrice,
            stockValue: stockValue,
          ),
        );

        // Add to category totals
        stockValueByCategory[category] =
            (stockValueByCategory[category] ?? 0) + stockValue;
      }

      return InventoryReportData(
        date: asOfDate ?? DateTime.now(),
        totalProducts: totalProducts,
        totalStockValue: totalStockValue,
        lowStockProducts: lowStockProducts,
        outOfStockProducts: outOfStockProducts,
        productStocks: productStocks,
        stockValueByCategory: stockValueByCategory,
      );
    } catch (e) {
      print('Error getting inventory report: $e');
      return _getDemoInventoryReport(asOfDate ?? DateTime.now());
    }
  }

  // Get finance report
  static Future<FinanceReportData> getFinanceReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoFinanceReport(startDate, endDate);
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final startDateStr = startDate.toIso8601String().split('T')[0];
      final endDateStr = endDate.toIso8601String().split('T')[0];

      // Get payments data
      final paymentsResponse = await _supabase!
          .from('payments')
          .select('type, category, amount, payment_date')
          .gte('payment_date', startDateStr)
          .lte('payment_date', endDateStr);

      double totalReceipts = 0;
      double totalPayments = 0;
      Map<String, double> receiptsByCategory = {};
      Map<String, double> paymentsByCategory = {};

      for (final payment in paymentsResponse) {
        final amount = (payment['amount'] as num).toDouble();
        final type = payment['type'] as String;
        final category = payment['category'] as String? ?? 'Khác';

        if (type == 'income') {
          totalReceipts += amount;
          receiptsByCategory[category] =
              (receiptsByCategory[category] ?? 0) + amount;
        } else if (type == 'expense') {
          totalPayments += amount;
          paymentsByCategory[category] =
              (paymentsByCategory[category] ?? 0) + amount;
        }
      }

      final netCashFlow = totalReceipts - totalPayments;
      const openingBalance = 5000000.0; // Default opening balance
      final closingBalance = openingBalance + netCashFlow;

      return FinanceReportData(
        date: DateTime.now(),
        totalReceipts: totalReceipts,
        totalPayments: totalPayments,
        netCashFlow: netCashFlow,
        openingBalance: openingBalance,
        closingBalance: closingBalance,
        receiptsByCategory: receiptsByCategory,
        paymentsByCategory: paymentsByCategory,
      );
    } catch (e) {
      print('Error getting finance report: $e');
      return _getDemoFinanceReport(startDate, endDate);
    }
  }

  // Get recent stock movements
  static Future<List<Map<String, dynamic>>> getRecentStockMovements({
    int limit = 10,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return [];
    }

    try {
      final response = await _supabase!
          .from('stock_transactions')
          .select('''
            id, type, quantity, notes, created_at,
            products!inner(name)
          ''')
          .order('created_at', ascending: false)
          .limit(limit);

      return response
          .map(
            (data) => {
              'id': data['id'],
              'type': data['type'],
              'product_name': data['products']['name'],
              'quantity': data['quantity'],
              'notes': data['notes'],
              'created_at': data['created_at'],
            },
          )
          .toList();
    } catch (e) {
      print('Error getting recent stock movements: $e');
      return [];
    }
  }

  // Get sales trend data
  static Future<List<Map<String, dynamic>>> getSalesTrend({
    required DateTime startDate,
    required DateTime endDate,
    ReportPeriod period = ReportPeriod.daily,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoSalesTrend(startDate, endDate, period);
    }

    // Real implementation would go here
    return _getDemoSalesTrend(startDate, endDate, period);
  }

  // Get top selling products
  static Future<List<TopSellingProduct>> getTopSellingProducts({
    required DateTime startDate,
    required DateTime endDate,
    int limit = 10,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoTopSellingProducts(limit);
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      final startDateStr = startDate.toIso8601String().split('T')[0];
      final endDateStr = endDate.toIso8601String().split('T')[0];

      // Get order items with product info
      final orderItemsResponse = await _supabase!
          .from('order_items')
          .select('''
            product_id, quantity, unit_price, total_amount,
            orders!inner(created_at, status),
            products!inner(name)
          ''')
          .gte('orders.created_at', startDateStr)
          .lte('orders.created_at', '${endDateStr}T23:59:59')
          .eq('orders.status', 'completed');

      // Group by product and calculate totals
      Map<String, Map<String, dynamic>> productSales = {};

      for (final item in orderItemsResponse) {
        final productId = item['product_id'] as String;
        final quantity = (item['quantity'] as num).toInt();
        final revenue = (item['total_amount'] as num).toDouble();
        final productName = item['products']['name'] as String;

        if (productSales.containsKey(productId)) {
          productSales[productId]!['quantity'] += quantity;
          productSales[productId]!['revenue'] += revenue;
        } else {
          productSales[productId] = {
            'name': productName,
            'quantity': quantity,
            'revenue': revenue,
          };
        }
      }

      // Convert to TopSellingProduct list and sort by quantity
      List<TopSellingProduct> topProducts = productSales.entries
          .map(
            (entry) => TopSellingProduct(
              productId: entry.key,
              productName: entry.value['name'],
              quantitySold: entry.value['quantity'],
              revenue: entry.value['revenue'],
            ),
          )
          .toList();

      topProducts.sort((a, b) => b.quantitySold.compareTo(a.quantitySold));

      return topProducts.take(limit).toList();
    } catch (e) {
      print('Error getting top selling products: $e');
      return _getDemoTopSellingProducts(limit);
    }
  }

  // Create sample data for testing
  static Future<void> createSampleData() async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      print('Cannot create sample data in demo mode');
      return;
    }

    try {
      print('Creating sample data...');

      // Create sample orders
      final sampleOrders = [
        {
          'id': '550e8400-e29b-41d4-a716-446655440001',
          'order_number': 'ORD-001',
          'type': 'sale',
          'status': 'completed',
          'subtotal': 100000,
          'tax_amount': 10000,
          'total_amount': 110000,
          'payment_method': 'cash',
          'created_at': '2025-06-01T10:00:00Z',
        },
        {
          'id': '550e8400-e29b-41d4-a716-446655440002',
          'order_number': 'ORD-002',
          'type': 'sale',
          'status': 'completed',
          'subtotal': 150000,
          'tax_amount': 15000,
          'total_amount': 165000,
          'payment_method': 'card',
          'created_at': '2025-06-01T14:30:00Z',
        },
        {
          'id': '550e8400-e29b-41d4-a716-446655440003',
          'order_number': 'ORD-003',
          'type': 'sale',
          'status': 'completed',
          'subtotal': 200000,
          'tax_amount': 20000,
          'total_amount': 220000,
          'payment_method': 'cash',
          'created_at': '2025-06-02T09:15:00Z',
        },
      ];

      // Insert orders
      for (final order in sampleOrders) {
        await _supabase!.from('orders').upsert(order);
      }

      print('Sample orders created successfully');
    } catch (e) {
      print('Error creating sample data: $e');
    }
  }

  // Get low stock products
  static Future<List<ProductStockInfo>> getLowStockProducts({
    int limit = 20,
  }) async {
    if (SupabaseConfig.isDemoMode || _supabase == null) {
      return _getDemoLowStockProducts(limit);
    }

    // REAL SUPABASE IMPLEMENTATION
    try {
      // Get products with low stock (stock_quantity <= 10)
      final productsResponse = await _supabase!
          .from('products')
          .select('''
            id, name, stock_quantity, cost, category_id,
            categories(name)
          ''')
          .lte('stock_quantity', 10)
          .order('stock_quantity', ascending: true)
          .limit(limit);

      List<ProductStockInfo> lowStockProducts = [];

      for (final product in productsResponse) {
        final stockQuantity = (product['stock_quantity'] as num?)?.toInt() ?? 0;
        final costPrice = (product['cost'] as num?)?.toDouble() ?? 0;
        final category =
            product['categories']?['name'] as String? ?? 'Chưa phân loại';

        lowStockProducts.add(
          ProductStockInfo(
            productId: product['id'],
            productName: product['name'],
            category: category,
            currentStock: stockQuantity,
            minStock: 10, // Default minimum stock threshold
            unitCost: costPrice,
            stockValue: stockQuantity * costPrice,
          ),
        );
      }

      return lowStockProducts;
    } catch (e) {
      print('Error getting low stock products: $e');
      return _getDemoLowStockProducts(limit);
    }
  }

  // Demo data methods
  static SalesReportData _getDemoSalesReport(
    DateTime startDate,
    DateTime endDate,
  ) {
    final now = DateTime.now();

    return SalesReportData(
      date: now,
      totalRevenue: 15750000,
      totalCost: 9450000,
      grossProfit: 6300000,
      totalOrders: 45,
      totalItems: 127,
      averageOrderValue: 350000,
      revenueByCategory: {
        'Đồ uống': 8500000,
        'Thức ăn': 5250000,
        'Bánh kẹo': 2000000,
      },
      ordersByPaymentMethod: {'Tiền mặt': 25, 'Thẻ': 15, 'Chuyển khoản': 5},
      topProducts: _getDemoTopSellingProducts(5),
    );
  }

  static InventoryReportData _getDemoInventoryReport(DateTime asOfDate) {
    return InventoryReportData(
      date: asOfDate,
      totalProducts: 50,
      totalStockValue: 125000000,
      lowStockProducts: 8,
      outOfStockProducts: 2,
      productStocks: _getDemoProductStocks(),
      stockValueByCategory: {
        'Đồ uống': 45000000,
        'Thức ăn': 55000000,
        'Bánh kẹo': 25000000,
      },
    );
  }

  static FinanceReportData _getDemoFinanceReport(
    DateTime startDate,
    DateTime endDate,
  ) {
    return FinanceReportData(
      date: DateTime.now(),
      totalReceipts: 18500000,
      totalPayments: 12300000,
      netCashFlow: 6200000,
      openingBalance: 5000000,
      closingBalance: 11200000,
      receiptsByCategory: {'Bán hàng': 15750000, 'Thu khác': 2750000},
      paymentsByCategory: {
        'Mua hàng': 4500000,
        'Lương': 3500000,
        'Thuê mặt bằng': 2000000,
        'Tiện ích': 800000,
        'Chi khác': 1500000,
      },
    );
  }

  static List<Map<String, dynamic>> _getDemoSalesTrend(
    DateTime startDate,
    DateTime endDate,
    ReportPeriod period,
  ) {
    final now = DateTime.now();
    final List<Map<String, dynamic>> trend = [];

    // Generate demo trend data for the last 7 days
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final revenue = 2000000 + (i * 250000) + (i % 2 * 500000);

      trend.add({
        'date': date.toIso8601String(),
        'revenue': revenue,
        'orders': 15 + (i * 2),
        'profit': revenue * 0.4,
      });
    }

    return trend;
  }

  static List<TopSellingProduct> _getDemoTopSellingProducts(int limit) {
    final products = [
      TopSellingProduct(
        productId: '1',
        productName: 'Cà phê đen',
        quantitySold: 45,
        revenue: 1125000,
      ),
      TopSellingProduct(
        productId: '2',
        productName: 'Cà phê sữa',
        quantitySold: 38,
        revenue: 1140000,
      ),
      TopSellingProduct(
        productId: '3',
        productName: 'Bánh mì thịt',
        quantitySold: 25,
        revenue: 875000,
      ),
      TopSellingProduct(
        productId: '4',
        productName: 'Nước cam',
        quantitySold: 32,
        revenue: 640000,
      ),
      TopSellingProduct(
        productId: '5',
        productName: 'Bánh ngọt',
        quantitySold: 28,
        revenue: 420000,
      ),
      TopSellingProduct(
        productId: '6',
        productName: 'Trà đá',
        quantitySold: 55,
        revenue: 550000,
      ),
    ];

    return products.take(limit).toList();
  }

  static List<ProductStockInfo> _getDemoLowStockProducts(int limit) {
    final products = [
      ProductStockInfo(
        productId: '1',
        productName: 'Cà phê đen',
        category: 'Đồ uống',
        currentStock: 5,
        minStock: 10,
        unitCost: 15000,
        stockValue: 75000,
      ),
      ProductStockInfo(
        productId: '3',
        productName: 'Bánh mì thịt',
        category: 'Thức ăn',
        currentStock: 3,
        minStock: 5,
        unitCost: 25000,
        stockValue: 75000,
      ),
      ProductStockInfo(
        productId: '7',
        productName: 'Sữa tươi',
        category: 'Đồ uống',
        currentStock: 0,
        minStock: 20,
        unitCost: 12000,
        stockValue: 0,
      ),
    ];

    return products.take(limit).toList();
  }

  static List<ProductStockInfo> _getDemoProductStocks() {
    return [
      ProductStockInfo(
        productId: '1',
        productName: 'Cà phê đen',
        category: 'Đồ uống',
        currentStock: 50,
        minStock: 10,
        unitCost: 15000,
        stockValue: 750000,
      ),
      ProductStockInfo(
        productId: '2',
        productName: 'Cà phê sữa',
        category: 'Đồ uống',
        currentStock: 45,
        minStock: 10,
        unitCost: 18000,
        stockValue: 810000,
      ),
      ProductStockInfo(
        productId: '3',
        productName: 'Bánh mì thịt',
        category: 'Thức ăn',
        currentStock: 20,
        minStock: 5,
        unitCost: 25000,
        stockValue: 500000,
      ),
      ProductStockInfo(
        productId: '4',
        productName: 'Nước cam',
        category: 'Đồ uống',
        currentStock: 30,
        minStock: 15,
        unitCost: 12000,
        stockValue: 360000,
      ),
      ProductStockInfo(
        productId: '5',
        productName: 'Bánh ngọt',
        category: 'Bánh kẹo',
        currentStock: 25,
        minStock: 10,
        unitCost: 8000,
        stockValue: 200000,
      ),
    ];
  }
}
