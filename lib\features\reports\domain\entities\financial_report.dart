class FinancialReport {
  final String id;
  final DateTime startDate;
  final DateTime endDate;
  final double totalRevenue;
  final double totalExpenses;
  final double grossProfit;
  final double netProfit;
  final double profitMargin;
  final Map<String, double> expenses;
  final List<Map<String, dynamic>> revenueByCategory;
  final List<Map<String, dynamic>> monthlyTrend;
  final DateTime createdAt;

  const FinancialReport({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.totalRevenue,
    required this.totalExpenses,
    required this.grossProfit,
    required this.netProfit,
    required this.profitMargin,
    required this.expenses,
    required this.revenueByCategory,
    required this.monthlyTrend,
    required this.createdAt,
  });

  factory FinancialReport.fromJson(Map<String, dynamic> json) {
    return FinancialReport(
      id: json['id'] as String,
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      totalRevenue: (json['total_revenue'] as num).toDouble(),
      totalExpenses: (json['total_expenses'] as num).toDouble(),
      grossProfit: (json['gross_profit'] as num).toDouble(),
      netProfit: (json['net_profit'] as num).toDouble(),
      profitMargin: (json['profit_margin'] as num).toDouble(),
      expenses: Map<String, double>.from(json['expenses'] as Map),
      revenueByCategory: List<Map<String, dynamic>>.from(json['revenue_by_category'] as List),
      monthlyTrend: List<Map<String, dynamic>>.from(json['monthly_trend'] as List),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'total_revenue': totalRevenue,
      'total_expenses': totalExpenses,
      'gross_profit': grossProfit,
      'net_profit': netProfit,
      'profit_margin': profitMargin,
      'expenses': expenses,
      'revenue_by_category': revenueByCategory,
      'monthly_trend': monthlyTrend,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Calculated properties
  double get expenseRatio => 
      totalRevenue > 0 ? (totalExpenses / totalRevenue) * 100 : 0.0;

  double get revenueGrowth {
    if (monthlyTrend.length < 2) return 0.0;
    
    final firstMonth = monthlyTrend.first['revenue'] as double;
    final lastMonth = monthlyTrend.last['revenue'] as double;
    
    return firstMonth > 0 ? ((lastMonth - firstMonth) / firstMonth) * 100 : 0.0;
  }

  double get profitGrowth {
    if (monthlyTrend.length < 2) return 0.0;
    
    final firstMonth = monthlyTrend.first['profit'] as double;
    final lastMonth = monthlyTrend.last['profit'] as double;
    
    return firstMonth > 0 ? ((lastMonth - firstMonth) / firstMonth) * 100 : 0.0;
  }

  String get largestExpenseCategory {
    if (expenses.isEmpty) return '';
    
    return expenses.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  String get topRevenueCategory {
    if (revenueByCategory.isEmpty) return '';
    
    return revenueByCategory
        .reduce((a, b) => (a['revenue'] as double) > (b['revenue'] as double) ? a : b)['category'] as String;
  }

  double get averageMonthlyRevenue {
    if (monthlyTrend.isEmpty) return 0.0;
    
    final totalRevenue = monthlyTrend.fold<double>(
      0.0, 
      (sum, month) => sum + (month['revenue'] as double)
    );
    
    return totalRevenue / monthlyTrend.length;
  }

  double get averageMonthlyProfit {
    if (monthlyTrend.isEmpty) return 0.0;
    
    final totalProfit = monthlyTrend.fold<double>(
      0.0, 
      (sum, month) => sum + (month['profit'] as double)
    );
    
    return totalProfit / monthlyTrend.length;
  }

  bool get isProfitable => netProfit > 0;

  String get financialHealth {
    if (profitMargin >= 20) return 'Excellent';
    if (profitMargin >= 15) return 'Good';
    if (profitMargin >= 10) return 'Fair';
    if (profitMargin >= 5) return 'Poor';
    return 'Critical';
  }

  FinancialReport copyWith({
    String? id,
    DateTime? startDate,
    DateTime? endDate,
    double? totalRevenue,
    double? totalExpenses,
    double? grossProfit,
    double? netProfit,
    double? profitMargin,
    Map<String, double>? expenses,
    List<Map<String, dynamic>>? revenueByCategory,
    List<Map<String, dynamic>>? monthlyTrend,
    DateTime? createdAt,
  }) {
    return FinancialReport(
      id: id ?? this.id,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      totalRevenue: totalRevenue ?? this.totalRevenue,
      totalExpenses: totalExpenses ?? this.totalExpenses,
      grossProfit: grossProfit ?? this.grossProfit,
      netProfit: netProfit ?? this.netProfit,
      profitMargin: profitMargin ?? this.profitMargin,
      expenses: expenses ?? this.expenses,
      revenueByCategory: revenueByCategory ?? this.revenueByCategory,
      monthlyTrend: monthlyTrend ?? this.monthlyTrend,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'FinancialReport(id: $id, totalRevenue: $totalRevenue, netProfit: $netProfit, profitMargin: $profitMargin%)';
  }
}
