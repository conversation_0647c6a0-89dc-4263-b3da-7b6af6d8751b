class SalesReport {
  final String id;
  final DateTime startDate;
  final DateTime endDate;
  final String period; // daily, weekly, monthly, yearly
  final double totalSales;
  final int totalOrders;
  final double averageOrderValue;
  final int totalCustomers;
  final int newCustomers;
  final int returningCustomers;
  final List<Map<String, dynamic>> salesByDay;
  final List<Map<String, dynamic>> salesByHour;
  final List<Map<String, dynamic>> topProducts;
  final List<Map<String, dynamic>> salesByCategory;
  final Map<String, double> paymentMethods;
  final DateTime createdAt;

  const SalesReport({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.period,
    required this.totalSales,
    required this.totalOrders,
    required this.averageOrderValue,
    required this.totalCustomers,
    required this.newCustomers,
    required this.returningCustomers,
    required this.salesByDay,
    required this.salesByHour,
    required this.topProducts,
    required this.salesByCategory,
    required this.paymentMethods,
    required this.createdAt,
  });

  factory SalesReport.fromJson(Map<String, dynamic> json) {
    return SalesReport(
      id: json['id'] as String,
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      period: json['period'] as String,
      totalSales: (json['total_sales'] as num).toDouble(),
      totalOrders: json['total_orders'] as int,
      averageOrderValue: (json['average_order_value'] as num).toDouble(),
      totalCustomers: json['total_customers'] as int,
      newCustomers: json['new_customers'] as int,
      returningCustomers: json['returning_customers'] as int,
      salesByDay: List<Map<String, dynamic>>.from(json['sales_by_day'] as List),
      salesByHour: List<Map<String, dynamic>>.from(json['sales_by_hour'] as List),
      topProducts: List<Map<String, dynamic>>.from(json['top_products'] as List),
      salesByCategory: List<Map<String, dynamic>>.from(json['sales_by_category'] as List),
      paymentMethods: Map<String, double>.from(json['payment_methods'] as Map),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'period': period,
      'total_sales': totalSales,
      'total_orders': totalOrders,
      'average_order_value': averageOrderValue,
      'total_customers': totalCustomers,
      'new_customers': newCustomers,
      'returning_customers': returningCustomers,
      'sales_by_day': salesByDay,
      'sales_by_hour': salesByHour,
      'top_products': topProducts,
      'sales_by_category': salesByCategory,
      'payment_methods': paymentMethods,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // Calculated properties
  double get customerRetentionRate => 
      totalCustomers > 0 ? (returningCustomers / totalCustomers) * 100 : 0.0;

  double get newCustomerRate => 
      totalCustomers > 0 ? (newCustomers / totalCustomers) * 100 : 0.0;

  double get salesGrowth {
    if (salesByDay.length < 2) return 0.0;
    
    final firstDay = salesByDay.first['sales'] as double;
    final lastDay = salesByDay.last['sales'] as double;
    
    return firstDay > 0 ? ((lastDay - firstDay) / firstDay) * 100 : 0.0;
  }

  Map<String, dynamic> get peakSalesHour {
    if (salesByHour.isEmpty) return {};
    
    return salesByHour.reduce((a, b) => 
        (a['sales'] as double) > (b['sales'] as double) ? a : b);
  }

  String get mostPopularPaymentMethod {
    if (paymentMethods.isEmpty) return '';
    
    return paymentMethods.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  SalesReport copyWith({
    String? id,
    DateTime? startDate,
    DateTime? endDate,
    String? period,
    double? totalSales,
    int? totalOrders,
    double? averageOrderValue,
    int? totalCustomers,
    int? newCustomers,
    int? returningCustomers,
    List<Map<String, dynamic>>? salesByDay,
    List<Map<String, dynamic>>? salesByHour,
    List<Map<String, dynamic>>? topProducts,
    List<Map<String, dynamic>>? salesByCategory,
    Map<String, double>? paymentMethods,
    DateTime? createdAt,
  }) {
    return SalesReport(
      id: id ?? this.id,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      period: period ?? this.period,
      totalSales: totalSales ?? this.totalSales,
      totalOrders: totalOrders ?? this.totalOrders,
      averageOrderValue: averageOrderValue ?? this.averageOrderValue,
      totalCustomers: totalCustomers ?? this.totalCustomers,
      newCustomers: newCustomers ?? this.newCustomers,
      returningCustomers: returningCustomers ?? this.returningCustomers,
      salesByDay: salesByDay ?? this.salesByDay,
      salesByHour: salesByHour ?? this.salesByHour,
      topProducts: topProducts ?? this.topProducts,
      salesByCategory: salesByCategory ?? this.salesByCategory,
      paymentMethods: paymentMethods ?? this.paymentMethods,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'SalesReport(id: $id, period: $period, totalSales: $totalSales, totalOrders: $totalOrders)';
  }
}
