import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/widgets/app_card.dart';
import '../../domain/entities/inventory_report.dart';

class InventoryReportWidget extends StatelessWidget {
  final InventoryReport report;

  const InventoryReportWidget({super.key, required this.report});

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          _buildSummaryCards(context, currencyFormat),
          const SizedBox(height: 24),

          // Stock Health and Categories
          LayoutBuilder(
            builder: (context, constraints) {
              if (constraints.maxWidth < 600) {
                return Column(
                  children: [
                    _buildStockHealth(context),
                    const SizedBox(height: 16),
                    _buildCategoriesChart(context),
                  ],
                );
              } else {
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: _buildStockHealth(context)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildCategoriesChart(context)),
                  ],
                );
              }
            },
          ),

          const SizedBox(height: 24),

          // Low Stock Items
          _buildLowStockItems(context),

          const SizedBox(height: 24),

          // Recent Movements
          _buildRecentMovements(context),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(BuildContext context, NumberFormat currencyFormat) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // If screen is too narrow, use wrap instead of row
        if (constraints.maxWidth < 800) {
          return Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              SizedBox(
                width: (constraints.maxWidth - 16) / 2,
                child: _buildSummaryCard(
                  context,
                  'Tổng sản phẩm',
                  '${report.totalProducts}',
                  Icons.inventory,
                  Colors.blue,
                ),
              ),
              SizedBox(
                width: (constraints.maxWidth - 16) / 2,
                child: _buildSummaryCard(
                  context,
                  'Giá trị kho',
                  currencyFormat.format(report.totalValue),
                  Icons.monetization_on,
                  Colors.green,
                ),
              ),
              SizedBox(
                width: (constraints.maxWidth - 16) / 2,
                child: _buildSummaryCard(
                  context,
                  'Sắp hết hàng',
                  '${report.lowStockProducts}',
                  Icons.warning,
                  Colors.orange,
                ),
              ),
              SizedBox(
                width: (constraints.maxWidth - 16) / 2,
                child: _buildSummaryCard(
                  context,
                  'Hết hàng',
                  '${report.outOfStockProducts}',
                  Icons.error,
                  Colors.red,
                ),
              ),
            ],
          );
        } else {
          return Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  context,
                  'Tổng sản phẩm',
                  '${report.totalProducts}',
                  Icons.inventory,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  context,
                  'Giá trị kho',
                  currencyFormat.format(report.totalValue),
                  Icons.monetization_on,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  context,
                  'Sắp hết hàng',
                  '${report.lowStockProducts}',
                  Icons.warning,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  context,
                  'Hết hàng',
                  '${report.outOfStockProducts}',
                  Icons.error,
                  Colors.red,
                ),
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'HIỆN TẠI',
                  style: TextStyle(
                    color: color,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStockHealth(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tình trạng kho hàng',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Stock health percentage
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Text(
                  'Tình trạng tổng thể',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  '${report.stockHealthPercentage.toStringAsFixed(1)}%',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: report.stockHealthPercentage / 100,
                  backgroundColor: Colors.grey.withValues(alpha: 0.2),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Stock breakdown
          _buildStockBreakdown(context),
        ],
      ),
    );
  }

  Widget _buildStockBreakdown(BuildContext context) {
    final healthyProducts =
        report.totalProducts -
        report.lowStockProducts -
        report.outOfStockProducts;

    return Column(
      children: [
        _buildStockRow(
          context,
          'Bình thường',
          healthyProducts,
          report.totalProducts,
          Colors.green,
        ),
        const SizedBox(height: 8),
        _buildStockRow(
          context,
          'Sắp hết',
          report.lowStockProducts,
          report.totalProducts,
          Colors.orange,
        ),
        const SizedBox(height: 8),
        _buildStockRow(
          context,
          'Hết hàng',
          report.outOfStockProducts,
          report.totalProducts,
          Colors.red,
        ),
      ],
    );
  }

  Widget _buildStockRow(
    BuildContext context,
    String label,
    int count,
    int total,
    Color color,
  ) {
    final percentage = total > 0 ? (count / total) * 100 : 0.0;

    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(child: Text(label)),
        Text(
          '$count (${percentage.toStringAsFixed(1)}%)',
          style: TextStyle(fontWeight: FontWeight.w500, color: color),
        ),
      ],
    );
  }

  Widget _buildCategoriesChart(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sản phẩm theo danh mục',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...report.categories.entries.map((entry) {
            final category = entry.key;
            final count = entry.value;
            final percentage = report.totalCategoryProducts > 0
                ? (count / report.totalCategoryProducts) * 100
                : 0.0;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [Text(category), Text('$count sản phẩm')],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey.withValues(alpha: 0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getCategoryColor(category),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildLowStockItems(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sản phẩm cần nhập thêm',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          if (report.lowStockItems.isEmpty)
            const Center(child: Text('Tất cả sản phẩm đều đủ hàng'))
          else
            ...report.lowStockItems.map((item) {
              final name = item['productName'] as String;
              final currentStock = item['currentStock'] as int;
              final minStock = item['minStock'] as int;
              final status = item['status'] as String;

              return ListTile(
                contentPadding: EdgeInsets.zero,
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _getStockStatusColor(status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getStockStatusIcon(status),
                    color: _getStockStatusColor(status),
                  ),
                ),
                title: Text(name),
                subtitle: Text('Tồn kho: $currentStock / Tối thiểu: $minStock'),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStockStatusColor(status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getStockStatusLabel(status),
                    style: TextStyle(
                      color: _getStockStatusColor(status),
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              );
            }),
        ],
      ),
    );
  }

  Widget _buildRecentMovements(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Biến động gần đây',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          if (report.recentMovements.isEmpty)
            const Center(child: Text('Chưa có biến động nào'))
          else
            ...report.recentMovements.take(5).map((movement) {
              final date = movement['date'] as DateTime;
              final productName = movement['productName'] as String;
              final type = movement['type'] as String;
              final quantity = movement['quantity'] as int;
              final reason = movement['reason'] as String;

              return ListTile(
                contentPadding: EdgeInsets.zero,
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _getMovementTypeColor(type).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getMovementTypeIcon(type),
                    color: _getMovementTypeColor(type),
                  ),
                ),
                title: Text(productName),
                subtitle: Text(reason),
                trailing: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${type == 'in' ? '+' : '-'}$quantity',
                      style: TextStyle(
                        color: _getMovementTypeColor(type),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${date.day}/${date.month}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              );
            }),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Đồ uống':
        return Colors.blue;
      case 'Thức ăn':
        return Colors.green;
      case 'Bánh kẹo':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Color _getStockStatusColor(String status) {
    switch (status) {
      case 'low':
        return Colors.orange;
      case 'out':
        return Colors.red;
      default:
        return Colors.green;
    }
  }

  IconData _getStockStatusIcon(String status) {
    switch (status) {
      case 'low':
        return Icons.warning;
      case 'out':
        return Icons.error;
      default:
        return Icons.check_circle;
    }
  }

  String _getStockStatusLabel(String status) {
    switch (status) {
      case 'low':
        return 'Sắp hết';
      case 'out':
        return 'Hết hàng';
      default:
        return 'Bình thường';
    }
  }

  Color _getMovementTypeColor(String type) {
    switch (type) {
      case 'in':
        return Colors.green;
      case 'out':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getMovementTypeIcon(String type) {
    switch (type) {
      case 'in':
        return Icons.arrow_downward;
      case 'out':
        return Icons.arrow_upward;
      default:
        return Icons.swap_horiz;
    }
  }
}
