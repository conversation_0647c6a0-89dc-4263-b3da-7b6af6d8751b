import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/widgets/app_card.dart';
import '../../domain/entities/sales_report.dart';

class SalesReportWidget extends StatelessWidget {
  final SalesReport report;

  const SalesReportWidget({super.key, required this.report});

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          _buildSummaryCards(context, currencyFormat),
          const SizedBox(height: 24),

          // Charts Section
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: _buildSalesChart(context)),
              const SizedBox(width: 16),
              Expanded(child: _buildCategory<PERSON>hart(context, currencyFormat)),
            ],
          ),

          const SizedBox(height: 24),

          // Top Products
          _buildTopProducts(context, currencyFormat),

          const SizedBox(height: 24),

          // Payment Methods
          _buildPaymentMethods(context, currencyFormat),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(BuildContext context, NumberFormat currencyFormat) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // If screen is too narrow, use wrap instead of row
        if (constraints.maxWidth < 800) {
          return Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              SizedBox(
                width: (constraints.maxWidth - 16) / 2,
                child: _buildSummaryCard(
                  context,
                  'Tổng doanh thu',
                  currencyFormat.format(report.totalSales),
                  Icons.monetization_on,
                  Colors.green,
                ),
              ),
              SizedBox(
                width: (constraints.maxWidth - 16) / 2,
                child: _buildSummaryCard(
                  context,
                  'Tổng đơn hàng',
                  '${report.totalOrders}',
                  Icons.shopping_cart,
                  Colors.blue,
                ),
              ),
              SizedBox(
                width: (constraints.maxWidth - 16) / 2,
                child: _buildSummaryCard(
                  context,
                  'Giá trị TB/đơn',
                  currencyFormat.format(report.averageOrderValue),
                  Icons.trending_up,
                  Colors.orange,
                ),
              ),
              SizedBox(
                width: (constraints.maxWidth - 16) / 2,
                child: _buildSummaryCard(
                  context,
                  'Khách hàng',
                  '${report.totalCustomers}',
                  Icons.people,
                  Colors.purple,
                ),
              ),
            ],
          );
        } else {
          return Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  context,
                  'Tổng doanh thu',
                  currencyFormat.format(report.totalSales),
                  Icons.monetization_on,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  context,
                  'Tổng đơn hàng',
                  '${report.totalOrders}',
                  Icons.shopping_cart,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  context,
                  'Giá trị TB/đơn',
                  currencyFormat.format(report.averageOrderValue),
                  Icons.trending_up,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  context,
                  'Khách hàng',
                  '${report.totalCustomers}',
                  Icons.people,
                  Colors.purple,
                ),
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  report.period.toUpperCase(),
                  style: TextStyle(
                    color: color,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesChart(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Doanh thu theo ngày',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          SizedBox(height: 200, child: _buildSimpleLineChart(context)),
        ],
      ),
    );
  }

  Widget _buildSimpleLineChart(BuildContext context) {
    if (report.salesByDay.isEmpty) {
      return const Center(child: Text('Không có dữ liệu'));
    }

    final maxSales = report.salesByDay
        .map((day) => day['sales'] as double)
        .reduce((a, b) => a > b ? a : b);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: report.salesByDay.take(7).map((day) {
        final sales = day['sales'] as double;
        final height = (sales / maxSales) * 150;
        final date = day['date'] as DateTime;

        return Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
              width: 20,
              height: height,
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(4),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${date.day}/${date.month}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildCategoryChart(
    BuildContext context,
    NumberFormat currencyFormat,
  ) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Doanh thu theo danh mục',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...report.salesByCategory.map((category) {
            final name = category['category'] as String;
            final revenue = category['revenue'] as double;
            final percentage = category['percentage'] as double;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(name),
                      Text(currencyFormat.format(revenue)),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey.withOpacity(0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getCategoryColor(name),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildTopProducts(BuildContext context, NumberFormat currencyFormat) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sản phẩm bán chạy',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...report.topProducts.take(5).map((product) {
            final name = product['productName'] as String;
            final quantity = product['quantity'] as int;
            final revenue = product['revenue'] as double;

            return ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.inventory_2, color: Colors.blue),
              ),
              title: Text(name),
              subtitle: Text('Số lượng: $quantity'),
              trailing: Text(
                currencyFormat.format(revenue),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPaymentMethods(
    BuildContext context,
    NumberFormat currencyFormat,
  ) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Phương thức thanh toán',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: report.paymentMethods.entries.map((entry) {
              final method = entry.key;
              final amount = entry.value;
              final percentage = (amount / report.totalSales) * 100;

              return Expanded(
                child: Column(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: _getPaymentMethodColor(
                          method,
                        ).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Icon(
                        _getPaymentMethodIcon(method),
                        color: _getPaymentMethodColor(method),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getPaymentMethodLabel(method),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      '${percentage.toStringAsFixed(1)}%',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      currencyFormat.format(amount),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Đồ uống':
        return Colors.blue;
      case 'Thức ăn':
        return Colors.green;
      case 'Bánh kẹo':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Color _getPaymentMethodColor(String method) {
    switch (method) {
      case 'cash':
        return Colors.green;
      case 'card':
        return Colors.blue;
      case 'transfer':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getPaymentMethodIcon(String method) {
    switch (method) {
      case 'cash':
        return Icons.money;
      case 'card':
        return Icons.credit_card;
      case 'transfer':
        return Icons.account_balance;
      default:
        return Icons.payment;
    }
  }

  String _getPaymentMethodLabel(String method) {
    switch (method) {
      case 'cash':
        return 'Tiền mặt';
      case 'card':
        return 'Thẻ';
      case 'transfer':
        return 'Chuyển khoản';
      default:
        return method;
    }
  }
}
