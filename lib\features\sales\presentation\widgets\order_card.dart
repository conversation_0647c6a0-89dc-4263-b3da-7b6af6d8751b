import 'package:flutter/material.dart';

import '../../../../core/themes/app_theme.dart';
import '../../../../data/models/order.dart';
import '../../../../generated/l10n/app_localizations.dart';

class OrderCard extends StatelessWidget {
  final Order order;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final Function(String)? onUpdateStatus;
  final Function(String)? onUpdatePaymentStatus;
  final VoidCallback? onDelete;

  const OrderCard({
    super.key,
    required this.order,
    this.onTap,
    this.onEdit,
    this.onUpdateStatus,
    this.onUpdatePaymentStatus,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order.displayOrderNumber,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: AppTheme.spacingXS),
                        Text(
                          order.partnerName,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        order.formattedTotal,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingXS),
                      Text(
                        '${order.totalItemsCount} ${l10n.items}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: AppTheme.spacingM),

              // Status badges
              Row(
                children: [
                  _buildStatusBadge(
                    context,
                    order.statusDisplayText,
                    Color(
                      int.parse(
                        'FF${order.statusColor.substring(1)}',
                        radix: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingS),
                  _buildStatusBadge(
                    context,
                    order.paymentStatusDisplayText,
                    Color(
                      int.parse(
                        'FF${order.paymentStatusColor.substring(1)}',
                        radix: 16,
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (order.createdAt != null)
                    Text(
                      _formatDate(order.createdAt!),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                ],
              ),

              // Action buttons
              if (onEdit != null ||
                  onDelete != null ||
                  onUpdateStatus != null ||
                  onUpdatePaymentStatus != null) ...[
                const SizedBox(height: AppTheme.spacingM),
                const Divider(),
                Row(
                  children: [
                    if (onEdit != null && order.canEdit)
                      TextButton.icon(
                        onPressed: onEdit,
                        icon: const Icon(Icons.edit, size: 16),
                        label: Text(l10n.edit),
                      ),

                    if (onUpdateStatus != null) ...[
                      const SizedBox(width: AppTheme.spacingS),
                      PopupMenuButton<String>(
                        onSelected: onUpdateStatus,
                        itemBuilder: (context) => [
                          if (order.status != 'confirmed')
                            PopupMenuItem(
                              value: 'confirmed',
                              child: Text(l10n.confirm),
                            ),
                          if (order.canComplete)
                            PopupMenuItem(
                              value: 'completed',
                              child: Text(l10n.complete),
                            ),
                          if (order.canCancel)
                            PopupMenuItem(
                              value: 'cancelled',
                              child: Text(l10n.cancel),
                            ),
                        ],
                        child: TextButton.icon(
                          onPressed: null,
                          icon: const Icon(Icons.more_vert, size: 16),
                          label: const Text('Trạng thái'),
                        ),
                      ),
                    ],

                    if (onUpdatePaymentStatus != null) ...[
                      const SizedBox(width: AppTheme.spacingS),
                      PopupMenuButton<String>(
                        onSelected: onUpdatePaymentStatus,
                        itemBuilder: (context) => [
                          if (order.paymentStatus != 'paid')
                            PopupMenuItem(
                              value: 'paid',
                              child: Text(l10n.markAsPaid),
                            ),
                          if (order.paymentStatus != 'partial')
                            PopupMenuItem(
                              value: 'partial',
                              child: Text(l10n.markAsPartial),
                            ),
                        ],
                        child: TextButton.icon(
                          onPressed: null,
                          icon: const Icon(Icons.payment, size: 16),
                          label: Text(l10n.payment),
                        ),
                      ),
                    ],

                    const Spacer(),

                    if (onDelete != null && order.canCancel)
                      IconButton(
                        onPressed: onDelete,
                        icon: const Icon(Icons.delete),
                        color: AppTheme.errorColor,
                        tooltip: l10n.delete,
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingS,
        vertical: AppTheme.spacingXS,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusS),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
