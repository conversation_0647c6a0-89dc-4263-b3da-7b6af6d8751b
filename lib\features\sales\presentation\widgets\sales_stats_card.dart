import 'package:flutter/material.dart';

import '../../../../core/themes/app_theme.dart';
import '../../../../core/utils/responsive.dart';
import '../../../../generated/l10n/app_localizations.dart';

class SalesStatsCard extends StatelessWidget {
  final Map<String, dynamic> stats;

  const SalesStatsCard({super.key, required this.stats});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final responsive = Responsive(context);

    final totalRevenue = (stats['total_revenue'] as num?)?.toDouble() ?? 0.0;
    final totalOrders = (stats['total_orders'] as num?)?.toInt() ?? 0;
    final completedOrders = (stats['completed_orders'] as num?)?.toInt() ?? 0;
    final pendingOrders = (stats['pending_orders'] as num?)?.toInt() ?? 0;
    final paidAmount = (stats['paid_amount'] as num?)?.toDouble() ?? 0.0;
    final pendingAmount = (stats['pending_amount'] as num?)?.toDouble() ?? 0.0;
    final averageOrderValue =
        (stats['average_order_value'] as num?)?.toDouble() ?? 0.0;

    final crossAxisCount = responsive.isMobile
        ? 2
        : responsive.isTablet
        ? 3
        : 4;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.salesOverview,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppTheme.spacingM),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: AppTheme.spacingM,
              mainAxisSpacing: AppTheme.spacingM,
              childAspectRatio: responsive.isMobile ? 1.5 : 2.0,
              children: [
                _buildStatItem(
                  context,
                  l10n.totalRevenue,
                  '${totalRevenue.toStringAsFixed(0)}đ',
                  Icons.attach_money,
                  Colors.green,
                ),
                _buildStatItem(
                  context,
                  l10n.totalOrders,
                  totalOrders.toString(),
                  Icons.shopping_cart,
                  Colors.blue,
                ),
                _buildStatItem(
                  context,
                  l10n.completedOrders,
                  completedOrders.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
                _buildStatItem(
                  context,
                  l10n.pendingOrders,
                  pendingOrders.toString(),
                  Icons.pending,
                  Colors.orange,
                ),
                _buildStatItem(
                  context,
                  l10n.paidAmount,
                  '${paidAmount.toStringAsFixed(0)}đ',
                  Icons.payment,
                  Colors.green,
                ),
                _buildStatItem(
                  context,
                  l10n.pendingAmount,
                  '${pendingAmount.toStringAsFixed(0)}đ',
                  Icons.schedule,
                  Colors.orange,
                ),
                _buildStatItem(
                  context,
                  l10n.averageOrderValue,
                  '${averageOrderValue.toStringAsFixed(0)}đ',
                  Icons.trending_up,
                  Colors.purple,
                ),
                _buildStatItem(
                  context,
                  l10n.conversionRate,
                  totalOrders > 0
                      ? '${((completedOrders / totalOrders) * 100).toStringAsFixed(1)}%'
                      : '0%',
                  Icons.analytics,
                  Colors.indigo,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingXS),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondaryColor),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
