import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';

void main() {
  group('Font Loading Tests', () {
    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('should load NotoSans font successfully', () async {
      try {
        final fontData = await rootBundle.load('assets/fonts/NotoSans-Regular.ttf');
        expect(fontData.lengthInBytes, greaterThan(0));
        print('✅ NotoSans font loaded: ${fontData.lengthInBytes} bytes');
      } catch (e) {
        fail('Failed to load NotoSans font: $e');
      }
    });

    test('should load Roboto font as fallback', () async {
      try {
        final fontData = await rootBundle.load('assets/fonts/Roboto-Regular.ttf');
        expect(fontData.lengthInBytes, greaterThan(0));
        print('✅ Roboto font loaded: ${fontData.lengthInBytes} bytes');
      } catch (e) {
        fail('Failed to load Roboto font: $e');
      }
    });

    test('should handle font loading priority', () async {
      final fontPaths = [
        'assets/fonts/NotoSans-Regular.ttf',
        'assets/fonts/Roboto-Regular.ttf',
        'packages/pdf/fonts/open_sans.ttf',
      ];

      String? loadedFont;
      for (final fontPath in fontPaths) {
        try {
          final fontData = await rootBundle.load(fontPath);
          if (fontData.lengthInBytes > 0) {
            loadedFont = fontPath;
            print('✅ Font loaded successfully: $fontPath (${fontData.lengthInBytes} bytes)');
            break;
          }
        } catch (e) {
          print('❌ Font loading failed for $fontPath: $e');
          continue;
        }
      }

      expect(loadedFont, isNotNull);
      expect(loadedFont, equals('assets/fonts/NotoSans-Regular.ttf'));
    });
  });
}
