import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Create a mock BuildContext
class MockBuildContext extends Mock implements BuildContext {}

// Create a mock NavigatorState
class MockNavigatorObserver extends Mock implements NavigatorObserver {}

// Create a mock Route
class MockRoute extends Mock implements Route<dynamic> {}

// Test utilities
class TestUtils {
  static Widget buildTestableWidget(Widget widget) {
    return MaterialApp(
      home: widget,
      navigatorObservers: [MockNavigatorObserver()],
    );
  }
  
  static Widget buildTestableWidgetWithScaffold(Widget widget) {
    return MaterialApp(
      home: Scaffold(
        body: widget,
      ),
      navigatorObservers: [MockNavigatorObserver()],
    );
  }
  
  static Widget buildTestableWidgetWithProvider(Widget widget, List<Override> overrides) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(
        home: widget,
        navigatorObservers: [MockNavigatorObserver()],
      ),
    );
  }
  
  static Widget buildTestableWidgetWithScaffoldAndProvider(Widget widget, List<Override> overrides) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(
        home: Scaffold(
          body: widget,
        ),
        navigatorObservers: [MockNavigatorObserver()],
      ),
    );
  }
  
  // Helper to pump widget with size
  static Future<void> pumpWidgetWithSize(
    WidgetTester tester,
    Widget widget, {
    Size size = const Size(1080, 1920),
  }) async {
    // ignore: deprecated_member_use
    tester.binding.window.physicalSizeTestValue = size;
    // ignore: deprecated_member_use
    tester.binding.window.devicePixelRatioTestValue = 1.0;
    
    await tester.pumpWidget(
      MaterialApp(
        home: Material(child: widget),
      ),
    );
    await tester.pumpAndSettle();
  }
}

// Mock for the BuildContext
final mockContext = MockBuildContext();

// Mock for the NavigatorObserver
final mockNavigatorObserver = MockNavigatorObserver();

// Mock for the Route
final mockRoute = MockRoute();
