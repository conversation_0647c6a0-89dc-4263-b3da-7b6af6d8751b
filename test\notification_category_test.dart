import 'package:flutter_test/flutter_test.dart';
import '../lib/features/notifications/domain/entities/notification.dart';

void main() {
  group('Notification Category Tests', () {
    test('should create newOrder notification with correct category', () {
      final notification = AppNotification.newOrder(
        orderNumber: 'ORD-001',
        totalAmount: 100000,
        customerName: 'Test Customer',
      );

      expect(notification.category, equals('sales'));
      expect(notification.type, equals('info'));
      expect(notification.title, equals('Đơn hàng mới'));
      expect(notification.message, contains('ORD-001'));
      expect(notification.message, contains('Test Customer'));
      expect(notification.message, contains('100000₫'));
    });

    test('should create lowStock notification with correct category', () {
      final notification = AppNotification.lowStock(
        productName: 'Cà phê đen',
        currentStock: 5,
        minStock: 10,
      );

      expect(notification.category, equals('inventory'));
      expect(notification.type, equals('warning'));
      expect(notification.title, equals('Sản phẩm sắp hết hàng'));
      expect(notification.isImportant, isTrue);
    });

    test('should create paymentReceived notification with correct category', () {
      final notification = AppNotification.paymentReceived(
        orderNumber: 'ORD-001',
        amount: 100000,
        paymentMethod: 'Tiền mặt',
      );

      expect(notification.category, equals('finance'));
      expect(notification.type, equals('success'));
      expect(notification.title, equals('Thanh toán thành công'));
    });

    test('should create systemUpdate notification with correct category', () {
      final notification = AppNotification.systemUpdate(
        version: '2.1.0',
        description: 'Bug fixes and improvements',
      );

      expect(notification.category, equals('system'));
      expect(notification.type, equals('info'));
      expect(notification.title, equals('Cập nhật hệ thống'));
    });

    test('should create error notification with correct category', () {
      final notification = AppNotification.error(
        title: 'Database Error',
        message: 'Connection failed',
      );

      expect(notification.category, equals('system'));
      expect(notification.type, equals('error'));
      expect(notification.isImportant, isTrue);
    });

    test('should validate all categories are in allowed list', () {
      final allowedCategories = ['system', 'inventory', 'sales', 'finance', 'user'];
      
      final notifications = [
        AppNotification.newOrder(
          orderNumber: 'ORD-001',
          totalAmount: 100000,
          customerName: 'Test',
        ),
        AppNotification.lowStock(
          productName: 'Test Product',
          currentStock: 1,
          minStock: 5,
        ),
        AppNotification.paymentReceived(
          orderNumber: 'ORD-001',
          amount: 100000,
          paymentMethod: 'Cash',
        ),
        AppNotification.systemUpdate(
          version: '1.0.0',
          description: 'Update',
        ),
        AppNotification.error(
          title: 'Error',
          message: 'Test error',
        ),
      ];

      for (final notification in notifications) {
        expect(
          allowedCategories.contains(notification.category),
          isTrue,
          reason: 'Category "${notification.category}" is not in allowed list: $allowedCategories',
        );
      }
    });

    test('should validate all types are in allowed list', () {
      final allowedTypes = ['info', 'success', 'warning', 'error'];
      
      final notifications = [
        AppNotification.newOrder(
          orderNumber: 'ORD-001',
          totalAmount: 100000,
          customerName: 'Test',
        ),
        AppNotification.lowStock(
          productName: 'Test Product',
          currentStock: 1,
          minStock: 5,
        ),
        AppNotification.paymentReceived(
          orderNumber: 'ORD-001',
          amount: 100000,
          paymentMethod: 'Cash',
        ),
        AppNotification.systemUpdate(
          version: '1.0.0',
          description: 'Update',
        ),
        AppNotification.error(
          title: 'Error',
          message: 'Test error',
        ),
      ];

      for (final notification in notifications) {
        expect(
          allowedTypes.contains(notification.type),
          isTrue,
          reason: 'Type "${notification.type}" is not in allowed list: $allowedTypes',
        );
      }
    });
  });
}
