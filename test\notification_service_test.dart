import 'package:flutter_test/flutter_test.dart';
import 'package:city_pos/features/notifications/domain/entities/notification.dart';

void main() {
  group('Notification Service Tests', () {
    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('should create notification data with valid categories', () {
      final notification = AppNotification.newOrder(
        orderNumber: 'ORD-001',
        totalAmount: 100000,
        customerName: 'Test Customer',
      );

      // Simulate the data creation process in NotificationService.createNotification
      final data = {
        'title': notification.title,
        'message': notification.message,
        'type': notification.type,
        'category': notification.category,
        'is_read': notification.isRead,
        'is_important': notification.isImportant,
        'action_url': notification.actionUrl,
        'action_label': notification.actionLabel,
        'data': notification.data,
        'expires_at': notification.expiresAt?.toIso8601String(),
        'user_id': null,
      };

      // Verify data structure matches database expectations
      expect(data['category'], equals('sales'));
      expect(data['type'], equals('info'));
      expect(data['title'], isA<String>());
      expect(data['message'], isA<String>());
      expect(data['is_read'], isA<bool>());
      expect(data['is_important'], isA<bool>());
    });

    test('should validate category constraints', () {
      final validCategories = ['system', 'inventory', 'sales', 'finance', 'user'];
      final validTypes = ['info', 'success', 'warning', 'error'];

      // Test all factory methods
      final testNotifications = [
        AppNotification.newOrder(
          orderNumber: 'ORD-001',
          totalAmount: 100000,
          customerName: 'Test',
        ),
        AppNotification.lowStock(
          productName: 'Test Product',
          currentStock: 1,
          minStock: 5,
        ),
        AppNotification.paymentReceived(
          orderNumber: 'ORD-001',
          amount: 100000,
          paymentMethod: 'Cash',
        ),
        AppNotification.systemUpdate(
          version: '1.0.0',
          description: 'Update',
        ),
        AppNotification.error(
          title: 'Error',
          message: 'Test error',
        ),
      ];

      for (final notification in testNotifications) {
        expect(
          validCategories.contains(notification.category),
          isTrue,
          reason: 'Invalid category: ${notification.category}',
        );
        expect(
          validTypes.contains(notification.type),
          isTrue,
          reason: 'Invalid type: ${notification.type}',
        );
      }
    });

    test('should handle notification creation data correctly', () {
      final notification = AppNotification(
        id: 'test_id',
        title: 'Test Notification',
        message: 'Test message with Vietnamese: Thông báo thử nghiệm',
        type: 'info',
        category: 'sales',
        isRead: false,
        isImportant: true,
        data: {
          'orderNumber': 'ORD-001',
          'amount': 100000,
        },
        actionUrl: '/sales',
        actionLabel: 'Xem chi tiết',
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(days: 7)),
      );

      // Test toJson method
      final json = notification.toJson();
      expect(json['category'], equals('sales'));
      expect(json['type'], equals('info'));
      expect(json['title'], contains('Test Notification'));
      expect(json['message'], contains('Vietnamese'));
      expect(json['is_read'], isFalse);
      expect(json['is_important'], isTrue);
      expect(json['data'], isA<Map<String, dynamic>>());
      expect(json['action_url'], equals('/sales'));
      expect(json['action_label'], equals('Xem chi tiết'));
    });

    test('should create valid notification for order creation', () {
      final orderNumber = 'HD-20250101-001';
      final totalAmount = 250000.0;
      final customerName = 'Nguyễn Văn A';

      final notification = AppNotification.newOrder(
        orderNumber: orderNumber,
        totalAmount: totalAmount,
        customerName: customerName,
      );

      // Verify notification properties
      expect(notification.category, equals('sales')); // Fixed from 'order'
      expect(notification.type, equals('info'));
      expect(notification.title, equals('Đơn hàng mới'));
      expect(notification.message, contains(orderNumber));
      expect(notification.message, contains(customerName));
      expect(notification.message, contains('250000₫'));
      expect(notification.actionUrl, equals('/sales'));
      expect(notification.actionLabel, equals('Xem đơn hàng'));
      expect(notification.data?['orderNumber'], equals(orderNumber));
      expect(notification.data?['totalAmount'], equals(totalAmount));
      expect(notification.data?['customerName'], equals(customerName));
    });

    test('should create valid notification for payment received', () {
      final orderNumber = 'HD-20250101-001';
      final amount = 250000.0;
      final paymentMethod = 'Tiền mặt';

      final notification = AppNotification.paymentReceived(
        orderNumber: orderNumber,
        amount: amount,
        paymentMethod: paymentMethod,
      );

      // Verify notification properties
      expect(notification.category, equals('finance'));
      expect(notification.type, equals('success'));
      expect(notification.title, equals('Thanh toán thành công'));
      expect(notification.message, contains(orderNumber));
      expect(notification.message, contains('250000₫'));
      expect(notification.actionUrl, equals('/finance'));
      expect(notification.actionLabel, equals('Xem sổ quỹ'));
    });

    test('should create valid notification for low stock', () {
      final productName = 'Cà phê đen đá';
      final currentStock = 3;
      final minStock = 10;

      final notification = AppNotification.lowStock(
        productName: productName,
        currentStock: currentStock,
        minStock: minStock,
      );

      // Verify notification properties
      expect(notification.category, equals('inventory'));
      expect(notification.type, equals('warning'));
      expect(notification.title, equals('Sản phẩm sắp hết hàng'));
      expect(notification.message, contains(productName));
      expect(notification.message, contains('3'));
      expect(notification.message, contains('10'));
      expect(notification.isImportant, isTrue);
      expect(notification.actionUrl, equals('/inventory'));
      expect(notification.actionLabel, equals('Xem kho hàng'));
    });
  });
}
