import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Mock data class
class OrderItem {
  final String name;
  final double price;
  final int quantity;

  OrderItem({required this.name, required this.price, required this.quantity});
}

void main() {
  Future<void> pumpOrderSummaryWidget(WidgetTester tester, {List<OrderItem> items = const [], double total = 0.0}) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: OrderSummaryWidget(
            items: items,
            total: total,
          ),
        ),
      ),
    );
    await tester.pump();
  }

  group('OrderSummaryWidget UI Tests', () {
    testWidgets('renders empty order summary widget', (WidgetTester tester) async {
      await pumpOrderSummaryWidget(tester);

      expect(find.text('Order Summary'), findsOneWidget);
      expect(find.text('No items in order'), findsOneWidget);
      expect(find.text('Total: 0.0'), findsOneWidget);
    });

    testWidgets('renders order summary widget with items', (WidgetTester tester) async {
      final items = [
        OrderItem(name: 'Coffee', price: 5.0, quantity: 2),
        OrderItem(name: 'Tea', price: 3.0, quantity: 1),
      ];
      await pumpOrderSummaryWidget(tester, items: items, total: 13.0);

      expect(find.text('Order Summary'), findsOneWidget);
      expect(find.text('Coffee'), findsOneWidget);
      expect(find.text('Tea'), findsOneWidget);
      expect(find.text('Total: 13.0'), findsOneWidget);
    });
  });
}

class OrderSummaryWidget extends StatelessWidget {
  final List<OrderItem> items;
  final double total;

  const OrderSummaryWidget({
    Key? key,
    required this.items,
    required this.total,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('Order Summary'),
        if (items.isEmpty)
          Text('No items in order')
        else
          ...items.map((item) => ListTile(
                title: Text(item.name),
                subtitle: Text('${item.price} x ${item.quantity}'),
                trailing: Text((item.price * item.quantity).toStringAsFixed(1)),
              )),
        Text('Total: ${total.toStringAsFixed(1)}'),
      ],
    );
  }
}
