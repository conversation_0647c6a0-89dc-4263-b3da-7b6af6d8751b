import 'package:city_pos/core/services/pdf_service.dart';
import 'package:city_pos/data/models/order.dart';
import 'package:city_pos/data/models/order_item.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PDF Font System Tests', () {
    setUpAll(() {
      // Initialize test binding
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('should generate PDF with font fallback system', () async {
      // Create test order
      final testOrder = Order(
        id: 'test-001',
        orderNumber: 'ORD-001',
        totalAmount: 100000,
        subtotal: 90000,
        discountAmount: 10000,
        taxAmount: 0,
        status: 'completed',
        paymentMethod: 'Tiền mặt',
        createdAt: DateTime.now(),
        items: [
          OrderItem(
            id: 'item-1',
            productId: 'prod-1',
            productName: 'Sản phẩm test với tiếng Việt',
            quantity: 2,
            unitPrice: 45000,
            totalAmount: 90000,
            totalPrice: 90000,
          ),
        ],
        notes: 'Khách hàng: <PERSON><PERSON>ễ<PERSON>ăn A - SĐT: 0123456789',
      );

      // Test PDF generation
      expect(() async {
        final pdfFile = await PDFService.generateInvoicePDF(testOrder);
        expect(pdfFile.existsSync(), isTrue);
        expect(pdfFile.path.contains('invoice_'), isTrue);

        // Clean up
        if (pdfFile.existsSync()) {
          await pdfFile.delete();
        }
      }, returnsNormally);
    });

    test('should handle Vietnamese text in PDF', () async {
      // Create order with Vietnamese text
      final vietnameseOrder = Order(
        id: 'vn-001',
        orderNumber: 'HĐ-001',
        totalAmount: 250000,
        subtotal: 250000,
        discountAmount: 0,
        taxAmount: 0,
        status: 'hoàn thành',
        paymentMethod: 'Chuyển khoản',
        createdAt: DateTime.now(),
        items: [
          OrderItem(
            id: 'item-vn-1',
            productId: 'prod-vn-1',
            productName: 'Bánh mì thịt nướng đặc biệt',
            quantity: 1,
            unitPrice: 25000,
            totalAmount: 25000,
            totalPrice: 25000,
          ),
          OrderItem(
            id: 'item-vn-2',
            productId: 'prod-vn-2',
            productName: 'Cà phê sữa đá truyền thống',
            quantity: 3,
            unitPrice: 75000,
            totalAmount: 225000,
            totalPrice: 225000,
          ),
        ],
        notes: 'Khách hàng: Trần Thị Bình - SĐT: 0987654321',
      );

      // Test PDF generation with Vietnamese content
      expect(() async {
        final pdfFile = await PDFService.generateInvoicePDF(vietnameseOrder);
        expect(pdfFile.existsSync(), isTrue);

        // Verify file size (should be reasonable)
        final fileSize = await pdfFile.length();
        expect(fileSize, greaterThan(1000)); // At least 1KB
        expect(fileSize, lessThan(1000000)); // Less than 1MB

        // Clean up
        if (pdfFile.existsSync()) {
          await pdfFile.delete();
        }
      }, returnsNormally);
    });
  });
}
