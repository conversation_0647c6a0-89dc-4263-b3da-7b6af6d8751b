import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock data class
class Order {
  final String id;
  final double total;

  Order({required this.id, required this.total});
}

// Mock callback
class MockOnOrderSelected extends Mock {
  void call(String orderId);
}

void main() {
  late MockOnOrderSelected mockOnOrderSelected;

  setUp(() {
    mockOnOrderSelected = MockOnOrderSelected();
  });

  Future<void> pumpRecentOrdersWidget(WidgetTester tester, {List<Order> orders = const []}) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: RecentOrdersWidget(
            orders: orders,
            onOrderSelected: mockOnOrderSelected.call,
          ),
        ),
      ),
    );
    await tester.pump();
  }

  group('RecentOrdersWidget UI Tests', () {
    testWidgets('renders empty recent orders widget', (WidgetTester tester) async {
      await pumpRecentOrdersWidget(tester);

      expect(find.text('Recent Orders'), findsOneWidget);
      expect(find.text('No recent orders'), findsOneWidget);
    });

    testWidgets('renders recent orders widget with orders', (WidgetTester tester) async {
      final orders = [
        Order(id: '1', total: 25.5),
        Order(id: '2', total: 30.0),
      ];
      await pumpRecentOrdersWidget(tester, orders: orders);

      expect(find.text('Recent Orders'), findsOneWidget);
      expect(find.text('Order #1'), findsOneWidget);
      expect(find.text('Order #2'), findsOneWidget);
    });

    testWidgets('tapping an order calls onOrderSelected with correct id', (WidgetTester tester) async {
      final orders = [Order(id: '1', total: 25.5)];
      await pumpRecentOrdersWidget(tester, orders: orders);

      await tester.tap(find.text('Order #1'));
      await tester.pump();

      verify(() => mockOnOrderSelected.call('1')).called(1);
    });
  });
}

class RecentOrdersWidget extends StatelessWidget {
  final List<Order> orders;
  final Function(String) onOrderSelected;

  const RecentOrdersWidget({
    Key? key,
    required this.orders,
    required this.onOrderSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('Recent Orders'),
        if (orders.isEmpty)
          Text('No recent orders')
        else
          Expanded(
            child: ListView.builder(
              itemCount: orders.length,
              itemBuilder: (context, index) {
                final order = orders[index];
                return ListTile(
                  title: Text('Order #${order.id}'),
                  subtitle: Text('Total: ${order.total.toStringAsFixed(1)}'),
                  onTap: () => onOrderSelected(order.id),
                );
              },
            ),
          ),
      ],
    );
  }
}
