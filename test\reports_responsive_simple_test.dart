import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Reports Responsive Design Tests', () {
    testWidgets('should handle mobile screen size correctly', (WidgetTester tester) async {
      // Set mobile screen size
      await tester.binding.setSurfaceSize(const Size(400, 800));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LayoutBuilder(
              builder: (context, constraints) {
                // Simulate the responsive logic from reports screen
                if (constraints.maxWidth < 800) {
                  return Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              height: 100,
                              margin: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Center(
                                child: Text(
                                  'Doanh thu',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 6), // Reduced spacing
                          Expanded(
                            child: Container(
                              height: 100,
                              margin: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Center(
                                child: Text(
                                  'Lợ<PERSON> nhuận',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6), // Reduced spacing
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              height: 100,
                              margin: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Center(
                                child: Text(
                                  'Đơn hàng',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 6), // Reduced spacing
                          Expanded(
                            child: Container(
                              height: 100,
                              margin: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.purple,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Center(
                                child: Text(
                                  'Tồn kho',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                } else {
                  return Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.all(16),
                          color: Colors.green,
                          child: const Center(child: Text('Doanh thu')),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.all(16),
                          color: Colors.blue,
                          child: const Center(child: Text('Lợi nhuận')),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.all(16),
                          color: Colors.orange,
                          child: const Center(child: Text('Đơn hàng')),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.all(16),
                          color: Colors.purple,
                          child: const Center(child: Text('Tồn kho')),
                        ),
                      ),
                    ],
                  );
                }
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify all 4 stat cards are present
      expect(find.text('Doanh thu'), findsOneWidget);
      expect(find.text('Lợi nhuận'), findsOneWidget);
      expect(find.text('Đơn hàng'), findsOneWidget);
      expect(find.text('Tồn kho'), findsOneWidget);

      // Verify they are arranged in 2x2 grid (Column with 2 Rows)
      expect(find.byType(Column), findsOneWidget);
      expect(find.byType(Row), findsNWidgets(2));
    });

    testWidgets('should handle desktop screen size correctly', (WidgetTester tester) async {
      // Set desktop screen size
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LayoutBuilder(
              builder: (context, constraints) {
                // Simulate the responsive logic from reports screen
                if (constraints.maxWidth < 800) {
                  return const Text('Mobile Layout');
                } else {
                  return Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.all(16),
                          color: Colors.green,
                          child: const Center(child: Text('Doanh thu')),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.all(16),
                          color: Colors.blue,
                          child: const Center(child: Text('Lợi nhuận')),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.all(16),
                          color: Colors.orange,
                          child: const Center(child: Text('Đơn hàng')),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Container(
                          height: 100,
                          margin: const EdgeInsets.all(16),
                          color: Colors.purple,
                          child: const Center(child: Text('Tồn kho')),
                        ),
                      ),
                    ],
                  );
                }
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify all 4 stat cards are present
      expect(find.text('Doanh thu'), findsOneWidget);
      expect(find.text('Lợi nhuận'), findsOneWidget);
      expect(find.text('Đơn hàng'), findsOneWidget);
      expect(find.text('Tồn kho'), findsOneWidget);

      // Verify they are arranged in single row
      expect(find.byType(Row), findsOneWidget);
      expect(find.byType(Column), findsNothing);
    });

    testWidgets('should verify spacing improvements', (WidgetTester tester) async {
      // Set mobile screen size
      await tester.binding.setSurfaceSize(const Size(400, 800));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                Row(
                  children: [
                    Expanded(child: Container(height: 80, color: Colors.red)),
                    const SizedBox(width: 6), // Tight spacing
                    Expanded(child: Container(height: 80, color: Colors.blue)),
                  ],
                ),
                const SizedBox(height: 6), // Tight spacing
                Row(
                  children: [
                    Expanded(child: Container(height: 80, color: Colors.green)),
                    const SizedBox(width: 6), // Tight spacing
                    Expanded(child: Container(height: 80, color: Colors.orange)),
                  ],
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find SizedBox widgets with width 6 (tight spacing)
      final horizontalSpacing = find.byWidgetPredicate(
        (widget) => widget is SizedBox && widget.width == 6.0,
      );
      expect(horizontalSpacing, findsNWidgets(2));

      // Find SizedBox widgets with height 6 (tight spacing)
      final verticalSpacing = find.byWidgetPredicate(
        (widget) => widget is SizedBox && widget.height == 6.0,
      );
      expect(verticalSpacing, findsOneWidget);
    });
  });
}
