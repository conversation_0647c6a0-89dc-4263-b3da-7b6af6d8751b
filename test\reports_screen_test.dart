import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mocktail/mocktail.dart';
import 'package:intl/intl.dart';
import '../lib/features/reports/presentation/reports_screen_simple.dart';
import '../lib/features/reports/domain/entities/report_data.dart';

// Mock for ReportsSimpleNotifier
class MockReportsSimpleNotifier extends StateNotifier<ReportsSimpleState> 
    with <PERSON><PERSON> implements ReportsSimpleNotifier {
  MockReportsSimpleNotifier() : super(ReportsSimpleState(
    startDate: DateTime.now().subtract(const Duration(days: 30)),
    endDate: DateTime.now(),
  ));
}

void main() {
  late MockReportsSimpleNotifier mockNotifier;
  late ReportsSimpleState testState;

  // Common test data setup
  setUp(() {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));
    
    testState = ReportsSimpleState(
      isLoading: false,
      salesReport: SalesReportData(
        date: now,
        totalRevenue: 1000000,
        totalCost: 600000,
        grossProfit: 400000,
        totalOrders: 25,
        totalItems: 100,
        averageOrderValue: 40000,
        revenueByCategory: {},
        ordersByPaymentMethod: {},
        topProducts: [],
      ),
      inventoryReport: InventoryReportData(
        date: now,
        totalProducts: 50,
        totalStockValue: 2000000,
        lowStockProducts: 5,
        outOfStockProducts: 2,
        productStocks: [],
        stockValueByCategory: {},
      ),
      financeReport: FinanceReportData(
        date: now,
        totalReceipts: 1000000,
        totalPayments: 600000,
        netCashFlow: 400000,
        openingBalance: 500000,
        closingBalance: 900000,
        receiptsByCategory: {},
        paymentsByCategory: {},
      ),
      salesTrend: [],
      topProducts: [],
      lowStockProducts: [],
      startDate: thirtyDaysAgo,
      endDate: now,
    );

    mockNotifier = MockReportsSimpleNotifier();
    when(() => mockNotifier.state).thenReturn(testState);
  });

  // Helper function to pump the widget with necessary providers
  Future<void> pumpReportsScreen(WidgetTester tester) async {
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          reportsSimpleProvider.overrideWith((ref) => mockNotifier),
        ],
        child: const MaterialApp(
          home: ReportsScreenSimple(),
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  group('ReportsScreenSimple', () {
    testWidgets('should show loading indicator when loading', 
      (WidgetTester tester) async {
        // Arrange - Set loading state
        when(() => mockNotifier.state).thenReturn(
          testState.copyWith(isLoading: true),
        );
        
        // Act
        await pumpReportsScreen(tester);
        
        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      },
    );

    testWidgets('should show error message when there is an error', 
      (WidgetTester tester) async {
        // Arrange - Set error state
        when(() => mockNotifier.state).thenReturn(
          testState.copyWith(
            error: 'Failed to load reports',
            isLoading: false,
          ),
        );
        
        // Act
        await pumpReportsScreen(tester);
        
        // Assert
        expect(find.text('Failed to load reports'), findsOneWidget);
      },
    );

    testWidgets('should display all stat cards with correct data', 
      (WidgetTester tester) async {
        // Act
        await pumpReportsScreen(tester);
        
        // Assert - Check for stat cards
        expect(find.text('Doanh thu'), findsOneWidget);
        expect(find.text('1,000,000'), findsOneWidget);
        
        expect(find.text('Lợi nhuận'), findsOneWidget);
        expect(find.text('400,000'), findsOneWidget);
        
        expect(find.text('Đơn hàng'), findsOneWidget);
        expect(find.text('25'), findsOneWidget);
        
        expect(find.text('Tồn kho'), findsOneWidget);
        expect(find.text('50'), findsOneWidget);
      },
    );

    testWidgets('should display date range in the header', 
      (WidgetTester tester) async {
        // Act
        await pumpReportsScreen(tester);
        
        // Assert - Check date range format
        final dateFormat = DateFormat('dd/MM/yyyy');
        final startDateStr = dateFormat.format(testState.startDate);
        final endDateStr = dateFormat.format(testState.endDate);
        
        expect(find.text('$startDateStr - $endDateStr'), findsOneWidget);
      },
    );
  });
}
