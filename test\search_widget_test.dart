import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:city_pos/generated/l10n/app_localizations.dart';

// Mock callback
class MockOnSearchChanged extends Mock {
  void call(String query);
}

void main() {
  late MockOnSearchChanged mockOnSearchChanged;

  setUp(() {
    mockOnSearchChanged = MockOnSearchChanged();
  });

  Future<void> pumpSearchWidget(WidgetTester tester, {String initialQuery = ''}) async {
    await tester.pumpWidget(
      MaterialApp(
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        home: Scaffold(
          body: SearchWidget(
            initialQuery: initialQuery,
            onSearchChanged: mockOnSearchChanged.call,
          ),
        ),
      ),
    );
    await tester.pumpAndSettle(); // Đảm bảo widget được render hoàn toàn
  }

  group('SearchWidget UI Tests', () {
    testWidgets('renders search widget with empty initial query', (WidgetTester tester) async {
      await pumpSearchWidget(tester);

      expect(find.byType(TextField), findsOneWidget);
      expect(find.byIcon(Icons.search), findsOneWidget);
      expect(find.byIcon(Icons.clear), findsNothing);
      expect(find.text(''), findsOneWidget);
    });

    testWidgets('renders search widget with initial query', (WidgetTester tester) async {
      await pumpSearchWidget(tester, initialQuery: 'Test Query');

      expect(find.byType(TextField), findsOneWidget);
      expect(find.byIcon(Icons.search), findsOneWidget);
      expect(find.byIcon(Icons.clear), findsOneWidget);
      expect(find.text('Test Query'), findsOneWidget);
    });

    testWidgets('typing in search field calls onSearchChanged', (WidgetTester tester) async {
      await pumpSearchWidget(tester);

      await tester.enterText(find.byType(TextField), 'New Query');
      await tester.pump();

      verify(() => mockOnSearchChanged.call('New Query')).called(1);
    });

    testWidgets('tapping clear button clears search field and calls onSearchChanged', (WidgetTester tester) async {
      await pumpSearchWidget(tester);

      // Kiểm tra xem trường tìm kiếm có hiển thị không trước khi nhập dữ liệu
      final searchFieldFinder = find.byKey(Key('search_field'));
      expect(searchFieldFinder, findsOneWidget, reason: 'Search field should be visible');

      // Nhập dữ liệu vào trường tìm kiếm
      await tester.enterText(searchFieldFinder, 'test');
      await tester.pumpAndSettle();

      // Kiểm tra xem nút clear có hiển thị không trước khi nhấn
      final clearButtonFinder = find.byIcon(Icons.clear);
      expect(clearButtonFinder, findsOneWidget, reason: 'Clear button should be visible after entering text');
      
      // Nhấn nút clear
      await tester.tap(clearButtonFinder);
      await tester.pumpAndSettle();

      // Kiểm tra xem nút clear có biến mất sau khi nhấn không
      expect(find.byIcon(Icons.clear), findsNothing, reason: 'Clear button should disappear after tapping');

      // Xác minh rằng onSearchChanged được gọi với chuỗi rỗng
      verify(() => mockOnSearchChanged.call('')).called(1);
    });
  });
}

class SearchWidget extends StatelessWidget {
  final String initialQuery;
  final Function(String) onSearchChanged;

  const SearchWidget({
    Key? key,
    required this.initialQuery,
    required this.onSearchChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final controller = TextEditingController(text: initialQuery);
    return TextField(
      key: Key('search_field'),
      controller: controller,
      decoration: InputDecoration(
        hintText: l10n.searchProducts,
        prefixIcon: Icon(Icons.search),
        suffixIcon: ValueListenableBuilder<TextEditingValue>(
          valueListenable: controller,
          builder: (context, value, child) {
            return value.text.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear),
                    onPressed: () {
                      controller.clear();
                      onSearchChanged('');
                    },
                  )
                : SizedBox.shrink();
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
      onChanged: onSearchChanged,
    );
  }
}
