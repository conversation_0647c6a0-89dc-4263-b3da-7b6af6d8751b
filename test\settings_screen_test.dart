import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock callbacks
class MockOnLanguageChanged extends Mock {
  void call(String language);
}

class MockOnThemeChanged extends Mock {
  void call(bool theme);
}

void main() {
  late MockOnLanguageChanged mockOnLanguageChanged;
  late MockOnThemeChanged mockOnThemeChanged;

  setUp(() {
    mockOnLanguageChanged = MockOnLanguageChanged();
    mockOnThemeChanged = MockOnThemeChanged();
  });

  Future<void> pumpSettingsScreen(WidgetTester tester, {String selectedLanguage = 'English', bool isDarkTheme = false}) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: SettingsScreen(
            selectedLanguage: selectedLanguage,
            isDarkTheme: isDarkTheme,
            onLanguageChanged: mockOnLanguageChanged.call,
            onThemeChanged: mockOnThemeChanged.call,
          ),
        ),
      ),
    );
    await tester.pump();
  }

  group('SettingsScreen UI Tests', () {
    testWidgets('renders settings screen with language and theme options', (WidgetTester tester) async {
      await pumpSettingsScreen(tester);

      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
      expect(find.byType(DropdownButton<String>), findsOneWidget);
      expect(find.byType(Switch), findsOneWidget);
    });

    testWidgets('changing language calls onLanguageChanged', (WidgetTester tester) async {
      await pumpSettingsScreen(tester);

      await tester.tap(find.byType(DropdownButton<String>));
      await tester.pump();
      await tester.tap(find.text('Spanish').last);
      await tester.pump();

      verify(() => mockOnLanguageChanged.call('Spanish')).called(1);
    });

    testWidgets('toggling theme calls onThemeChanged', (WidgetTester tester) async {
      await pumpSettingsScreen(tester, isDarkTheme: false);

      await tester.tap(find.byType(Switch));
      await tester.pump();

      verify(() => mockOnThemeChanged.call(true)).called(1);
    });
  });
}

class SettingsScreen extends StatelessWidget {
  final String selectedLanguage;
  final bool isDarkTheme;
  final Function(String) onLanguageChanged;
  final Function(bool) onThemeChanged;

  const SettingsScreen({
    Key? key,
    required this.selectedLanguage,
    required this.isDarkTheme,
    required this.onLanguageChanged,
    required this.onThemeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languages = [
      'English',
      'Spanish',
    ];

    return Column(
      children: [
        Text('Settings'),
        DropdownButton<String>(
          value: selectedLanguage,
          items: languages.map((lang) => DropdownMenuItem<String>(
            value: lang,
            child: Text(lang),
          )).toList(),
          onChanged: (String? value) {
            if (value != null) {
              onLanguageChanged(value);
            }
          },
        ),
        ListTile(
          title: Text('Theme'),
          trailing: Switch(
            value: isDarkTheme,
            onChanged: (value) => onThemeChanged(value),
          ),
        ),
      ],
    );
  }
}
